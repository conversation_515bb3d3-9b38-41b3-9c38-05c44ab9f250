#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/VoterInfo.hpp"

namespace NexusPro
{
    namespace CoreFixes
    {
        class CVote : public IModuleInterface, public SingletonHelper::CSingleton<CVote>
        {
        public:
            CVote() = default;
            virtual ~CVote() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "Vote"; }

            // Configuration
            void Configure(int32_t level, uint32_t playTime, double pvpPoint, double pvpCashBag,
                          int32_t classGrade, bool scoreListShow, bool scoreHide);

            // Public accessors
            bool ScoreListShow() const { return m_bScoreListShow; }
            bool ScoreHide() const { return m_bScoreHide; }

            // Condition checking
            bool CheckConditions(ATF::CPlayer* pPlayer, bool bView = false);

        private:
            bool m_bInitialized{ false };

            // Configuration values
            int32_t m_level{ 30 };
            uint32_t m_playTime{ UINT32_MAX };
            double m_pvpPoint{ -1.0 };
            double m_pvpCashBag{ -1.0 };
            int32_t m_classGrade{ -1 };
            bool m_bScoreListShow{ true };
            bool m_bScoreHide{ false };

            // Logging
            bool m_bLogVoteActions{ false };

        private:
            // Condition checking helpers
            bool CheckLevel(int32_t level) const;
            bool CheckClassGrade(int32_t classGrade) const;
            bool CheckPlayTime(uint32_t playTime) const;
            bool CheckPvpPoint(double pvpPoint) const;
            bool CheckPvpCashBag(double pvpCashBag) const;

        private:
            // Hook functions
            static int WINAPIV SendVotePaper(
                ATF::Voter* pObj,
                ATF::CPlayer* pPlayer,
                ATF::Info::Voter_SendVotePaper12_ptr next);

            static void WINAPIV SendVotePaperAll(
                ATF::Voter* pObj,
                ATF::Info::Voter_SendVotePaperAll14_ptr next);

            static void WINAPIV SendVoteScore(
                ATF::Voter* pObj,
                ATF::CPlayer* pPlayer,
                ATF::Info::Voter_SendVoteScore16_ptr next);

            static void WINAPIV SendVoteScoreAll(
                ATF::Voter* pObj,
                char byRace,
                ATF::Info::Voter_SendVoteScoreAll18_ptr next);

            static int WINAPIV Vote(
                ATF::Voter* pObj,
                ATF::CPlayer* pPlayer,
                char* pData,
                ATF::Info::Voter_Vote22_ptr next);

        private:
            static CVote* s_instance;
        };
    }
}
