#include "pch.h"
#include "PotionMgr.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CPotionMgr* CPotionMgr::s_instance = nullptr;

        bool CPotionMgr::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableStackingFix = configManager.GetModuleConfigBool("corefix.potion_mgr", "Fixes", "enable_stacking_fix", true);
            m_bEnableDelayFix = configManager.GetModuleConfigBool("corefix.potion_mgr", "Fixes", "enable_delay_fix", true);
            m_bEnableEffectLimitFix = configManager.GetModuleConfigBool("corefix.potion_mgr", "Fixes", "enable_effect_limit_fix", true);
            m_bEnableCashItemFix = configManager.GetModuleConfigBool("corefix.potion_mgr", "Fixes", "enable_cash_item_fix", true);
            m_bEnablePvpPotionFix = configManager.GetModuleConfigBool("corefix.potion_mgr", "Fixes", "enable_pvp_potion_fix", true);
            m_bLogPotionUsage = configManager.GetModuleConfigBool("corefix.potion_mgr", "Logging", "log_potion_usage", false);

            // Hook potion manager functions
            enable_hook(&ATF::CPotionMgr::PreCheckPotion, &CPotionMgr::PreCheckPotion);

            m_bInitialized = true;

            LOG_INFO("PotionMgr", "Potion manager fixes initialized - Stacking: " + std::to_string(m_bEnableStackingFix) +
                     ", Delay: " + std::to_string(m_bEnableDelayFix) + 
                     ", Effect Limit: " + std::to_string(m_bEnableEffectLimitFix) +
                     ", Cash Item: " + std::to_string(m_bEnableCashItemFix) +
                     ", PvP: " + std::to_string(m_bEnablePvpPotionFix));

            return true;
        }

        void CPotionMgr::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();
            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("PotionMgr", "Potion manager fixes shutdown");
        }

        void CPotionMgr::Configure(bool enableStackingFix, bool enableDelayFix, bool enableEffectLimitFix,
                                  bool enableCashItemFix, bool enablePvpPotionFix)
        {
            m_bEnableStackingFix = enableStackingFix;
            m_bEnableDelayFix = enableDelayFix;
            m_bEnableEffectLimitFix = enableEffectLimitFix;
            m_bEnableCashItemFix = enableCashItemFix;
            m_bEnablePvpPotionFix = enablePvpPotionFix;
        }

        int WINAPIV CPotionMgr::PreCheckPotion(
            ATF::CPotionMgr* pObj,
            ATF::CPlayer* pUsePlayer,
            ATF::CCharacter** pTargetCharacter,
            ATF::_PotionItem_fld* pfB,
            unsigned int nCurTime,
            ATF::_skill_fld* pFld,
            bool bCheckDist,
            ATF::Info::CPotionMgrPreCheckPotion22_ptr next)
        {
            if (!pObj || !pUsePlayer || !pfB || !pFld)
                return -1;

            auto pTrgChar = *pTargetCharacter;
            if (!pTrgChar)
                return 25;

            if (!s_instance)
                return next(pObj, pUsePlayer, pTargetCharacter, pfB, nCurTime, pFld, bCheckDist);

            // Cash item restriction check
            if (s_instance->m_bEnableCashItemFix && CheckCashItemRestriction(pFld))
            {
                if (s_instance->m_bLogPotionUsage)
                {
                    LOG_DEBUG("PotionMgr", "Blocked cash item potion usage - Player: " + 
                             std::string(pUsePlayer->m_wszPlayerName) + ", Effect Type: " + std::to_string(pFld->m_nTempEffectType));
                }
                return 25;
            }

            // Effect limitation check
            if (s_instance->m_bEnableEffectLimitFix && !CheckEffectLimitation(pUsePlayer, pFld))
            {
                if (s_instance->m_bLogPotionUsage)
                {
                    LOG_DEBUG("PotionMgr", "Blocked potion due to effect limitation - Player: " + 
                             std::string(pUsePlayer->m_wszPlayerName));
                }
                return 31;
            }

            // Potion stacking check
            if (s_instance->m_bEnableStackingFix && !CheckPotionStacking(pUsePlayer, pFld))
            {
                if (s_instance->m_bLogPotionUsage)
                {
                    LOG_DEBUG("PotionMgr", "Blocked potion stacking - Player: " + 
                             std::string(pUsePlayer->m_wszPlayerName));
                }
                return 31;
            }

            // Potion delay check
            if (s_instance->m_bEnableDelayFix && !CheckPotionDelay(pUsePlayer, nCurTime))
            {
                if (s_instance->m_bLogPotionUsage)
                {
                    LOG_DEBUG("PotionMgr", "Blocked potion due to delay - Player: " + 
                             std::string(pUsePlayer->m_wszPlayerName));
                }
                return 30;
            }

            // PvP potion restriction check
            if (s_instance->m_bEnablePvpPotionFix && !CheckPvpPotionRestriction(pUsePlayer, pFld))
            {
                if (s_instance->m_bLogPotionUsage)
                {
                    LOG_DEBUG("PotionMgr", "Blocked PvP potion restriction - Player: " + 
                             std::string(pUsePlayer->m_wszPlayerName));
                }
                return 25;
            }

            // Log successful potion usage
            if (s_instance->m_bLogPotionUsage)
            {
                LOG_DEBUG("PotionMgr", "Potion usage allowed - Player: " + 
                         std::string(pUsePlayer->m_wszPlayerName) + ", Effect Type: " + std::to_string(pFld->m_nTempEffectType));
            }

            // Call original function
            return next(pObj, pUsePlayer, pTargetCharacter, pfB, nCurTime, pFld, bCheckDist);
        }

        bool CPotionMgr::CheckEffectLimitation(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld)
        {
            if (!pPlayer || !pFld)
                return false;

            // Check for effect type limitations
            // This implements the CheckEffLimType macro logic from original
            int effectType = pFld->m_nTempEffectType;
            
            // Add specific effect limitation checks here based on server requirements
            // For now, allow all effects
            return true;
        }

        bool CPotionMgr::CheckPotionStacking(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld)
        {
            if (!pPlayer || !pFld)
                return false;

            // Check if player already has this effect active
            // Prevent stacking of certain potion effects
            // Implementation would depend on specific server rules
            return true;
        }

        bool CPotionMgr::CheckPotionDelay(ATF::CPlayer* pPlayer, unsigned int nCurTime)
        {
            if (!pPlayer)
                return false;

            // Check if enough time has passed since last potion use
            // Implementation would check player's last potion use time
            return true;
        }

        bool CPotionMgr::CheckCashItemRestriction(ATF::_skill_fld* pFld)
        {
            if (!pFld)
                return false;

            // Check for restricted cash item potion effects
            int effectType = pFld->m_nTempEffectType;
            
            // Block specific cash item effects (48, 49, 70, 71 from original)
            if (effectType == 48 || effectType == 49 || effectType == 70 || effectType == 71)
            {
                return true; // Restricted
            }

            return false; // Not restricted
        }

        bool CPotionMgr::CheckPvpPotionRestriction(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld)
        {
            if (!pPlayer || !pFld)
                return false;

            // Check for PvP-specific potion restrictions
            // Implementation would depend on server PvP rules
            return true; // Allow for now
        }
    }
}
