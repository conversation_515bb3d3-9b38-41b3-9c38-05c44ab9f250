#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CGuildInfo.hpp"

namespace NexusPro
{
    namespace CoreFixes
    {
        class CGuild : public IModuleInterface, public SingletonHelper::CSingleton<CGuild>
        {
        public:
            CGuild() = default;
            virtual ~CGuild() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "Guild"; }

            // Configuration
            void Configure(uint32_t battleCostGold, bool enableBattleFix, bool enableExpulseFix,
                          bool enableHonorGuildFix, bool logGuildActions);

        private:
            bool m_bInitialized{ false };

            // Configuration values
            uint32_t m_battleCostGold{ 5000 };
            bool m_bEnableBattleFix{ true };
            bool m_bEnableExpulseFix{ true };
            bool m_bEnableHonorGuildFix{ true };
            bool m_bLogGuildActions{ false };

        private:
            // Hook functions
            static char WINAPIV ManageAcceptORRefuseGuildBattle(
                ATF::CGuild* pObj,
                bool bAccept,
                ATF::Info::CGuildManageAcceptORRefuseGuildBattle80_ptr next);

            static char WINAPIV ManageExpulseMember(
                ATF::CGuild* pGuild,
                unsigned int dwMemberSerial,
                ATF::Info::CGuildManageExpulseMember84_ptr next);

            // Helper functions
            static bool ValidateGuildBattleRequest(ATF::CGuild* pGuild);
            static bool ValidateGuildBattleCost(ATF::CGuild* pGuild, uint32_t cost);
            static bool ValidateMemberExpulsion(ATF::CGuild* pGuild, unsigned int dwMemberSerial);

        private:
            static CGuild* s_instance;
        };
    }
}
