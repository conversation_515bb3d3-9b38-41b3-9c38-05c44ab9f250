#include "pch.h"
#include "Guild.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CGuild* CGuild::s_instance = nullptr;

        bool CGuild::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_battleCostGold = configManager.GetModuleConfigInt("corefix.guild", "Battle", "cost_gold", 5000);
            m_bEnableBattleFix = configManager.GetModuleConfigBool("corefix.guild", "Fixes", "enable_battle_fix", true);
            m_bEnableExpulseFix = configManager.GetModuleConfigBool("corefix.guild", "Fixes", "enable_expulse_fix", true);
            m_bEnableHonorGuildFix = configManager.GetModuleConfigBool("corefix.guild", "Fixes", "enable_honor_guild_fix", true);
            m_bLogGuildActions = configManager.GetModuleConfigBool("corefix.guild", "Logging", "log_guild_actions", false);

            // Hook guild functions
            if (m_bEnableBattleFix)
            {
                enable_hook(&ATF::CGuild::ManageAcceptORRefuseGuildBattle, &CGuild::ManageAcceptORRefuseGuildBattle);
            }

            if (m_bEnableExpulseFix)
            {
                enable_hook(&ATF::CGuild::ManageExpulseMember, &CGuild::ManageExpulseMember);
            }

            m_bInitialized = true;

            LOG_INFO("Guild", "Guild fixes initialized - Battle Fix: " + std::to_string(m_bEnableBattleFix) +
                     ", Expulse Fix: " + std::to_string(m_bEnableExpulseFix) +
                     ", Honor Guild Fix: " + std::to_string(m_bEnableHonorGuildFix) +
                     ", Battle Cost: " + std::to_string(m_battleCostGold) + " gold");

            return true;
        }

        void CGuild::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();
            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("Guild", "Guild fixes shutdown");
        }

        void CGuild::Configure(uint32_t battleCostGold, bool enableBattleFix, bool enableExpulseFix,
                              bool enableHonorGuildFix, bool logGuildActions)
        {
            m_battleCostGold = battleCostGold;
            m_bEnableBattleFix = enableBattleFix;
            m_bEnableExpulseFix = enableExpulseFix;
            m_bEnableHonorGuildFix = enableHonorGuildFix;
            m_bLogGuildActions = logGuildActions;
        }

        char WINAPIV CGuild::ManageAcceptORRefuseGuildBattle(
            ATF::CGuild* pObj,
            bool bAccept,
            ATF::Info::CGuildManageAcceptORRefuseGuildBattle80_ptr next)
        {
            if (!pObj || !s_instance)
                return next(pObj, bAccept);

            char result = 0;

            do
            {
                // Validate guild battle request state
                if (!ValidateGuildBattleRequest(pObj))
                {
                    result = 111; // Invalid request state
                    break;
                }

                if (bAccept)
                {
                    // Check if guild has enough gold for battle
                    if (!ValidateGuildBattleCost(pObj, s_instance->m_battleCostGold))
                    {
                        result = 116; // Insufficient gold
                        if (s_instance->m_bLogGuildActions)
                        {
                            LOG_DEBUG("Guild", "Guild battle rejected - insufficient gold. Guild: " + 
                                     std::string(pObj->m_wszName) + ", Required: " + std::to_string(s_instance->m_battleCostGold));
                        }
                        break;
                    }

                    // Log guild battle acceptance
                    if (s_instance->m_bLogGuildActions)
                    {
                        LOG_INFO("Guild", "Guild battle accepted - Guild: " + std::string(pObj->m_wszName) +
                                ", Cost: " + std::to_string(s_instance->m_battleCostGold) + " gold");
                    }

                    // Add guild battle to controller
                    result = ATF::CGuildBattleController::Instance()->Add(
                        pObj->m_GuildBattleSugestMatter.pkSrc,
                        pObj->m_GuildBattleSugestMatter.pkDest,
                        pObj->m_GuildBattleSugestMatter.dwStartTime,
                        pObj->m_GuildBattleSugestMatter.dwNumber,
                        pObj->m_GuildBattleSugestMatter.dwMapIdx);

                    if (result)
                    {
                        // Handle battle creation failure
                        if (result == 112)
                            result = 119;

                        pObj->m_GuildBattleSugestMatter.pkSrc->PushDQSInGuildBattleCost();
                        pObj->m_GuildBattleSugestMatter.pkSrc->SendMsg_ApplyGuildBattleResultInform(result, pObj->m_wszName);

                        if (s_instance->m_bLogGuildActions)
                        {
                            LOG_WARNING("Guild", "Guild battle creation failed - Result: " + std::to_string(result));
                        }
                    }
                    else
                    {
                        // Battle created successfully - deduct cost
                        pObj->PushDQSDestGuildOutputGuildBattleCost();

                        // Handle honor guild money update
                        if (s_instance->m_bEnableHonorGuildFix &&
                            ATF::CHonorGuild::Instance()->CheckHonorGuild(
                                pObj->m_GuildBattleSugestMatter.pkSrc->m_byRace,
                                pObj->m_GuildBattleSugestMatter.pkSrc->m_dwSerial))
                        {
                            ATF::CMoneySupplyMgr::Instance()->UpdateHonorGuildMoneyData(1, pObj->m_byRace, s_instance->m_battleCostGold);
                        }

                        if (s_instance->m_bLogGuildActions)
                        {
                            LOG_INFO("Guild", "Guild battle created successfully - Guild: " + std::string(pObj->m_wszName));
                        }
                    }
                }
                else
                {
                    // Guild battle refused
                    pObj->m_GuildBattleSugestMatter.pkSrc->SendMsg_GuildBattleRefused(pObj->m_wszName);
                    pObj->m_GuildBattleSugestMatter.pkSrc->PushDQSInGuildBattleCost();

                    if (s_instance->m_bLogGuildActions)
                    {
                        LOG_INFO("Guild", "Guild battle refused - Guild: " + std::string(pObj->m_wszName));
                    }
                }

                // Clear battle suggestion matter
                pObj->m_GuildBattleSugestMatter.pkSrc->m_GuildBattleSugestMatter.Clear();
                pObj->m_GuildBattleSugestMatter.Clear();

            } while (false);

            return result;
        }

        char WINAPIV CGuild::ManageExpulseMember(
            ATF::CGuild* pGuild,
            unsigned int dwMemberSerial,
            ATF::Info::CGuildManageExpulseMember84_ptr next)
        {
            if (!pGuild || !s_instance)
                return next(pGuild, dwMemberSerial);

            // Validate member expulsion
            if (!ValidateMemberExpulsion(pGuild, dwMemberSerial))
            {
                if (s_instance->m_bLogGuildActions)
                {
                    LOG_WARNING("Guild", "Member expulsion blocked - Guild: " + std::string(pGuild->m_wszName) +
                               ", Member Serial: " + std::to_string(dwMemberSerial));
                }
                return 1; // Expulsion not allowed
            }

            // Check if guild is in battle
            auto pInstance = ATF::GUILD_BATTLE::CNormalGuildBattleManager::Instance();
            LPVOID pBattle = pInstance->GetBattleByGuildSerial(pGuild->m_dwSerial);
            if (pBattle)
            {
                if (s_instance->m_bLogGuildActions)
                {
                    LOG_WARNING("Guild", "Member expulsion blocked during guild battle - Guild: " + 
                               std::string(pGuild->m_wszName));
                }
                return 1; // Cannot expel during battle
            }

            if (s_instance->m_bLogGuildActions)
            {
                LOG_INFO("Guild", "Member expelled - Guild: " + std::string(pGuild->m_wszName) +
                        ", Member Serial: " + std::to_string(dwMemberSerial));
            }

            // Call original function
            return next(pGuild, dwMemberSerial);
        }

        bool CGuild::ValidateGuildBattleRequest(ATF::CGuild* pGuild)
        {
            if (!pGuild)
                return false;

            return pGuild->m_GuildBattleSugestMatter.eState == ATF::_guild_battle_suggest_matter::APPLY_BATTLE_REQUEST_SUGGEST;
        }

        bool CGuild::ValidateGuildBattleCost(ATF::CGuild* pGuild, uint32_t cost)
        {
            if (!pGuild)
                return false;

            return pGuild->m_dTotalGold >= cost;
        }

        bool CGuild::ValidateMemberExpulsion(ATF::CGuild* pGuild, unsigned int dwMemberSerial)
        {
            if (!pGuild || dwMemberSerial == 0)
                return false;

            // Add additional validation logic here if needed
            // For example, check if member exists, check permissions, etc.
            return true;
        }
    }
}
