#include "pch.h"
#include "Store.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CStore* CStore::s_instance = nullptr;

        bool CStore::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableOverlapFix = configManager.GetModuleConfigBool("corefix.store", "Fixes", "enable_overlap_fix", true);
            m_bEnablePriceFix = configManager.GetModuleConfigBool("corefix.store", "Fixes", "enable_price_fix", true);
            m_bEnableDiscountFix = configManager.GetModuleConfigBool("corefix.store", "Fixes", "enable_discount_fix", true);
            m_bEnableOverflowFix = configManager.GetModuleConfigBool("corefix.store", "Fixes", "enable_overflow_fix", true);
            m_maxDiscountRate = configManager.GetModuleConfigFloat("corefix.store", "Limits", "max_discount_rate", 1.0f);
            m_bLogTransactions = configManager.GetModuleConfigBool("corefix.store", "Logging", "log_transactions", false);

            // Hook store functions
            enable_hook(&ATF::CItemStore::IsSell, &CStore::IsSell);

            m_bInitialized = true;

            LOG_INFO("Store", "Store system fixes initialized - Overlap Fix: " + std::to_string(m_bEnableOverlapFix) +
                     ", Price Fix: " + std::to_string(m_bEnablePriceFix) +
                     ", Discount Fix: " + std::to_string(m_bEnableDiscountFix) +
                     ", Overflow Fix: " + std::to_string(m_bEnableOverflowFix) +
                     ", Max Discount: " + std::to_string(m_maxDiscountRate));

            return true;
        }

        void CStore::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();
            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("Store", "Store system fixes shutdown");
        }

        void CStore::Configure(bool enableOverlapFix, bool enablePriceFix, bool enableDiscountFix,
                              bool enableOverflowFix, float maxDiscountRate, bool logTransactions)
        {
            m_bEnableOverlapFix = enableOverlapFix;
            m_bEnablePriceFix = enablePriceFix;
            m_bEnableDiscountFix = enableDiscountFix;
            m_bEnableOverflowFix = enableOverflowFix;
            m_maxDiscountRate = maxDiscountRate;
            m_bLogTransactions = logTransactions;
        }

        char WINAPIV CStore::IsSell(
            ATF::CItemStore* pObj,
            char byOfferNum,
            ATF::_buy_offer* pOffer,
            unsigned int dwHasDalant,
            unsigned int dwHasGold,
            long double dHasPoint,
            unsigned int* dwHasActPoint,
            char* pbyActCode,
            float fDiscountRate,
            char byRace,
            char byGrade,
            ATF::Info::CItemStoreIsSell36_ptr next)
        {
            if (!pObj || !pOffer || !s_instance)
                return next(pObj, byOfferNum, pOffer, dwHasDalant, dwHasGold, dHasPoint, dwHasActPoint, pbyActCode, fDiscountRate, byRace, byGrade);

            // Validate item overlap (prevent selling non-stackable items with quantity > 1)
            if (s_instance->m_bEnableOverlapFix && !ValidateItemOverlap(pObj, byOfferNum, pOffer))
            {
                if (s_instance->m_bLogTransactions)
                {
                    LOG_WARNING("Store", "Store transaction blocked - Invalid item overlap detected");
                }
                return 100; // Error code for invalid transaction
            }

            // Calculate prices for all money types
            std::array<uint64_t, static_cast<size_t>(MoneyType::Num)> summaryPrices{0};
            if (s_instance->m_bEnablePriceFix)
            {
                CalculatePrices(pObj, byOfferNum, pOffer, byRace, summaryPrices);
            }

            // Validate and apply discount
            if (s_instance->m_bEnableDiscountFix)
            {
                if (!ValidateDiscountRate(fDiscountRate, s_instance->m_maxDiscountRate))
                {
                    if (s_instance->m_bLogTransactions)
                    {
                        LOG_WARNING("Store", "Store transaction - Invalid discount rate adjusted: " + std::to_string(fDiscountRate));
                    }
                }
                ApplyDiscount(summaryPrices, fDiscountRate);
            }

            // Check for price overflow
            if (s_instance->m_bEnableOverflowFix && !ValidatePriceCalculation(summaryPrices))
            {
                if (s_instance->m_bLogTransactions)
                {
                    LOG_WARNING("Store", "Store transaction blocked - Price overflow detected");
                }
                return 100; // Error code for price overflow
            }

            if (s_instance->m_bLogTransactions)
            {
                LOG_DEBUG("Store", "Store transaction validated - Offers: " + std::to_string(byOfferNum) +
                         ", Race: " + std::to_string(byRace) + ", Discount: " + std::to_string(fDiscountRate));
            }

            // Call original function
            return next(pObj, byOfferNum, pOffer, dwHasDalant, dwHasGold, dHasPoint, dwHasActPoint, pbyActCode, fDiscountRate, byRace, byGrade);
        }

        bool CStore::ValidateItemOverlap(ATF::CItemStore* pStore, char byOfferNum, ATF::_buy_offer* pOffer)
        {
            if (!pStore || !pOffer)
                return false;

            for (int i = 0; i < byOfferNum; ++i)
            {
                char nTableCode = pStore->m_pStorageItem[pOffer[i].byGoodIndex].byItemTableCode;
                
                // Check if trying to sell multiple non-stackable items
                if (pOffer[i].byGoodAmount > 1 && ATF::Global::IsOverLapItem(nTableCode) == FALSE)
                {
                    return false; // Invalid overlap
                }
            }

            return true;
        }

        bool CStore::ValidatePriceCalculation(const std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices)
        {
            for (const auto& price : prices)
            {
                if (price >= UINT32_MAX)
                {
                    return false; // Price overflow
                }
            }
            return true;
        }

        bool CStore::ValidateDiscountRate(float& discountRate, float maxRate)
        {
            bool wasValid = true;

            if (discountRate < 0.0f)
            {
                discountRate = 0.0f;
                wasValid = false;
            }
            else if (discountRate > maxRate)
            {
                discountRate = maxRate;
                wasValid = false;
            }

            return wasValid;
        }

        void CStore::CalculatePrices(ATF::CItemStore* pStore, char byOfferNum, ATF::_buy_offer* pOffer,
                                    char byRace, std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices)
        {
            if (!pStore || !pOffer)
                return;

            float fRaceMultiplier = ATF::Global::eGetTex(byRace) + 1.0f;

            for (int i = 0; i < byOfferNum; ++i)
            {
                char byMoneyUnit = 0;
                uint64_t currentPrice = pStore->CalcSellPrice(pOffer[i].byGoodIndex, &byMoneyUnit);
                
                // Validate money unit index
                if (byMoneyUnit >= static_cast<char>(MoneyType::Num) || byMoneyUnit < 0)
                    continue;

                // Calculate total price with race multiplier and quantity
                prices[byMoneyUnit] += static_cast<uint64_t>(currentPrice * pOffer[i].byGoodAmount * fRaceMultiplier);
            }
        }

        void CStore::ApplyDiscount(std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices, float discountRate)
        {
            if (discountRate <= 0.0f)
                return;

            for (auto& price : prices)
            {
                price = static_cast<uint64_t>(price * (1.0f - discountRate));
            }
        }
    }
}
