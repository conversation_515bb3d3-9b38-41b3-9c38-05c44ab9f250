[General]
; Unmanned Trader System Fix Module
; This module fixes and enhances the unmanned trader system
enabled=true

[Features]
; Enable unmanned trader fixes
enable_trader_fixes=true

; Enable trader validation
enable_validation=true

; Enable trader monitoring
enable_monitoring=false

[Validation]
; Enable transaction validation
enable_transaction_validation=true

; Enable registration validation
enable_registration_validation=true

; Enable gold amount validation
enable_gold_validation=true

; Enable item validation
enable_item_validation=true

[Limits]
; Maximum transactions per hour per player
max_transactions_per_hour=100

; Maximum gold per transaction
max_gold_per_transaction=10000000

; Maximum items per registration
max_items_per_registration=20

; Maximum trader registrations per player
max_registrations_per_player=10

[Security]
; Enable anti-exploit measures
enable_anti_exploit=true

; Enable transaction rate limiting
enable_rate_limiting=true

; Minimum time between transactions (seconds)
min_transaction_interval=5

; Enable trader spam prevention
enable_spam_prevention=true

[Trading]
; Enable buy transaction fixes
enable_buy_fixes=true

; Enable registration fixes
enable_registration_fixes=true

; Enable re-registration fixes
enable_reregistration_fixes=true

; Enable notification fixes
enable_notification_fixes=true

[Performance]
; Enable trader data caching
enable_trader_caching=true

; Maximum cached trader entries
max_cached_entries=1000

; Cache cleanup interval (minutes)
cache_cleanup_interval=30

[Monitoring]
; Enable trader metrics collection
enable_metrics=false

; Enable trader statistics
enable_statistics=false

; Metrics reporting interval (minutes)
metrics_interval=60

[Logging]
; Enable trader activity logging
log_trader_activity=false

; Log buy transactions
log_buy_transactions=false

; Log registrations
log_registrations=false

; Log re-registrations
log_reregistrations=false

; Log blocked transactions
log_blocked_transactions=true

; Log validation failures
log_validation_failures=true

[Description]
; UnmannedTrader module provides:
; - Unmanned trader system fixes and stability improvements
; - Transaction validation and security measures
; - Anti-exploit and rate limiting protection
; - Trader registration and re-registration fixes
; - Buy transaction processing improvements
; - Performance optimizations for trader operations
; - Comprehensive trader activity monitoring
; - Trader metrics and statistics collection
