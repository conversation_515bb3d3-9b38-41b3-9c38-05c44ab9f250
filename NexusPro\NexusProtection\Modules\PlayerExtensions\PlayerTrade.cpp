#include "pch.h"
#include "PlayerTrade.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace PlayerExtensions
    {
        CPlayerTrade* CPlayerTrade::s_instance = nullptr;

        bool CPlayerTrade::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableTradeValidation = configManager.GetModuleConfigBool("playerext.trade", "Validation", "enable_trade_validation", true);
            m_bEnableRaceCheck = configManager.GetModuleConfigBool("playerext.trade", "Validation", "enable_race_check", true);
            m_bEnableDegradeCheck = configManager.GetModuleConfigBool("playerext.trade", "Validation", "enable_degrade_check", true);
            m_bEnableItemValidation = configManager.GetModuleConfigBool("playerext.trade", "Validation", "enable_item_validation", true);
            m_bEnableAntiDupe = configManager.GetModuleConfigBool("playerext.trade", "Security", "enable_anti_dupe", true);
            m_bLogTrades = configManager.GetModuleConfigBool("playerext.trade", "Logging", "log_trades", false);

            // Hook trade functions
            enable_hook(&ATF::CPlayer::pc_DTradeOKRequest, &CPlayerTrade::pc_DTradeOKRequest);

            m_bInitialized = true;

            LOG_INFO("PlayerTrade", "Player trade system initialized - Trade Validation: " + std::to_string(m_bEnableTradeValidation) +
                     ", Race Check: " + std::to_string(m_bEnableRaceCheck) +
                     ", Degrade Check: " + std::to_string(m_bEnableDegradeCheck) +
                     ", Item Validation: " + std::to_string(m_bEnableItemValidation) +
                     ", Anti-Dupe: " + std::to_string(m_bEnableAntiDupe));

            return true;
        }

        void CPlayerTrade::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();

            std::lock_guard<std::mutex> lock(m_tradeDataMutex);
            m_playerTradeData.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("PlayerTrade", "Player trade system shutdown");
        }

        void CPlayerTrade::Configure(bool enableTradeValidation, bool enableRaceCheck, bool enableDegradeCheck,
                                    bool enableItemValidation, bool enableAntiDupe, bool logTrades)
        {
            m_bEnableTradeValidation = enableTradeValidation;
            m_bEnableRaceCheck = enableRaceCheck;
            m_bEnableDegradeCheck = enableDegradeCheck;
            m_bEnableItemValidation = enableItemValidation;
            m_bEnableAntiDupe = enableAntiDupe;
            m_bLogTrades = logTrades;
        }

        void CPlayerTrade::InitializePlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_tradeDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            TradeData& data = m_playerTradeData[playerSerial];
            data.partnerSerial = 0;
            data.tradeStartTime = std::chrono::steady_clock::now();
            data.tradeAttempts = 0;
            data.isValidating = true;

            if (m_bLogTrades)
            {
                LOG_DEBUG("PlayerTrade", "Player trade tracking initialized: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void CPlayerTrade::CleanupPlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_tradeDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            m_playerTradeData.erase(playerSerial);

            if (m_bLogTrades)
            {
                LOG_DEBUG("PlayerTrade", "Player trade tracking cleaned up: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        bool CPlayerTrade::ValidateTradeRequest(ATF::CPlayer* pPlayer, ATF::CPlayer* pPartner)
        {
            if (!pPlayer || !pPartner || !m_bEnableTradeValidation)
                return true;

            // Check trade restrictions
            if (!CheckTradeRestrictions(pPlayer, pPartner))
                return false;

            // Check race compatibility
            if (m_bEnableRaceCheck && !CheckRaceCompatibility(pPlayer, pPartner))
                return false;

            // Check degrade compatibility
            if (m_bEnableDegradeCheck && !CheckDegradeCompatibility(pPlayer, pPartner))
                return false;

            // Check trade mode and lock status
            if (!CheckTradeMode(pPlayer) || !CheckTradeLock(pPlayer))
                return false;

            if (!CheckTradeMode(pPartner) || !CheckTradeLock(pPartner))
                return false;

            return true;
        }

        bool CPlayerTrade::ValidateTradeItems(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bEnableItemValidation)
                return true;

            return ValidateExchangeItems(pPlayer);
        }

        bool CPlayerTrade::ValidateItemExchangeability(ATF::CPlayer* pPlayer, uint32_t itemSerial, uint8_t storageCode)
        {
            if (!pPlayer || !m_bEnableItemValidation)
                return true;

            // Check storage code validity
            if (storageCode >= ATF::STORAGE_POS::STORAGE_NUM)
                return false;

            // Get item from storage
            auto pStoragePtr = pPlayer->m_Param.m_pStoragePtr[storageCode];
            if (!pStoragePtr)
                return false;

            auto pItem = pStoragePtr->GetPtrFromSerial(itemSerial);
            if (!pItem)
                return false;

            // Check if item is exchangeable
            // This would require implementation of ItemCheckHelper::IsExchangable equivalent
            // For now, we'll do basic validation
            return true;
        }

        bool CPlayerTrade::CheckTradeRestrictions(ATF::CPlayer* pPlayer, ATF::CPlayer* pPartner)
        {
            if (!pPlayer || !pPartner)
                return false;

            // Check if players are in valid trade state
            ATF::CPlayer* pDst = nullptr;
            if (!ATF::Global::DTradeEqualPerson(pPlayer, &pDst) || pDst != pPartner)
                return false;

            return true;
        }

        bool CPlayerTrade::CheckRaceCompatibility(ATF::CPlayer* pPlayer1, ATF::CPlayer* pPlayer2) const
        {
            if (!pPlayer1 || !pPlayer2)
                return false;

            // Check if players are of the same race
            return pPlayer1->m_Param.GetRaceCode() == pPlayer2->m_Param.GetRaceCode();
        }

        bool CPlayerTrade::CheckDegradeCompatibility(ATF::CPlayer* pPlayer1, ATF::CPlayer* pPlayer2) const
        {
            if (!pPlayer1 || !pPlayer2)
                return false;

            // Check degrade compatibility rules
            if (pPlayer1->m_byUserDgr && pPlayer2->m_byUserDgr == 0)
                return false;

            if (pPlayer2->m_byUserDgr && pPlayer1->m_byUserDgr == 0)
                return false;

            return true;
        }

        bool CPlayerTrade::CheckTradeMode(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer)
                return false;

            return pPlayer->m_pmTrd.bDTradeMode;
        }

        bool CPlayerTrade::CheckTradeLock(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer)
                return false;

            return pPlayer->m_pmTrd.bDTradeLock;
        }

        bool CPlayerTrade::ValidateExchangeItems(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer)
                return false;

            // Validate all items being traded
            for (int i = 0; i < pPlayer->m_pmTrd.bySellItemNum; ++i)
            {
                uint8_t storageCode = pPlayer->m_pmTrd.DItemNode[i].byStorageCode;
                uint32_t itemSerial = pPlayer->m_pmTrd.DItemNode[i].dwSerial;

                // Check storage code validity
                if (storageCode >= ATF::STORAGE_POS::STORAGE_NUM)
                    return false;

                // Get item from storage
                auto pStoragePtr = pPlayer->m_Param.m_pStoragePtr[storageCode];
                if (!pStoragePtr)
                    return false;

                auto pItem = pStoragePtr->GetPtrFromSerial(itemSerial);
                if (!pItem)
                    return false;

                // Additional item validation would go here
                // For now, basic validation passes
            }

            return true;
        }

        void WINAPIV CPlayerTrade::pc_DTradeOKRequest(
            ATF::CPlayer* pObj,
            unsigned int* pdwKey,
            ATF::Info::CPlayerpc_DTradeOKRequest1687_ptr next)
        {
            if (!pObj || !pdwKey || !s_instance)
            {
                next(pObj, pdwKey);
                return;
            }

            bool bCheckPassed = false;
            ATF::CPlayer* pDst = nullptr;

            do
            {
                // Check if trade is valid
                if (!ATF::Global::DTradeEqualPerson(pObj, &pDst))
                {
                    bCheckPassed = true;
                    break;
                }

                if (!pDst)
                    break;

                // Validate trade request
                if (!s_instance->ValidateTradeRequest(pObj, pDst))
                {
                    if (s_instance->m_bLogTrades)
                    {
                        LOG_WARNING("PlayerTrade", "Trade request validation failed - Player: " + 
                                   std::string(pObj->m_wszPlayerName) + ", Partner: " + std::string(pDst->m_wszPlayerName));
                    }
                    break;
                }

                // Validate trade items
                if (!s_instance->ValidateTradeItems(pObj) || !s_instance->ValidateTradeItems(pDst))
                {
                    if (s_instance->m_bLogTrades)
                    {
                        LOG_WARNING("PlayerTrade", "Trade item validation failed - Player: " + 
                                   std::string(pObj->m_wszPlayerName) + ", Partner: " + std::string(pDst->m_wszPlayerName));
                    }
                    break;
                }

                bCheckPassed = true;

            } while (false);

            if (bCheckPassed)
            {
                if (s_instance->m_bLogTrades)
                {
                    LOG_INFO("PlayerTrade", "Trade approved - Player: " + std::string(pObj->m_wszPlayerName) +
                            ", Partner: " + (pDst ? std::string(pDst->m_wszPlayerName) : "Unknown"));
                }
                next(pObj, pdwKey);
            }
            else if (s_instance->m_bLogTrades)
            {
                LOG_WARNING("PlayerTrade", "Trade blocked - Player: " + std::string(pObj->m_wszPlayerName));
            }
        }
    }
}
