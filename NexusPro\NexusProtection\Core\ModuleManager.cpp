#include "pch.h"
#include "ModuleManager.h"

// Include all embedded modules
#include "../Modules/CoreFixes/Player.h"
#include "../Modules/CoreFixes/AttackSystem.h"
#include "../Modules/CoreFixes/NetworkEx.h"
#include "../Modules/CoreFixes/CheatCommand.h"
#include "../Modules/CoreFixes/CrashDump.h"
#include "../Modules/CoreFixes/PerformanceMonitor.h"
#include "../Modules/CoreFixes/MainThread.h"
#include "../Modules/CoreFixes/PotionMgr.h"
#include "../Modules/CoreFixes/Guild.h"
#include "../Modules/CoreFixes/Vote.h"
#include "../Modules/CoreFixes/Store.h"
#include "../Modules/CoreFixes/Performance.h"
#include "../Modules/CoreFixes/PostSystem.h"
#include "../Modules/CoreFixes/UnmannedTrader.h"
#include "../Modules/PlayerExtensions/PlayerEx.h"
#include "../Modules/PlayerExtensions/PlayerMoveSystem.h"
#include "../Modules/PlayerExtensions/PlayerTrade.h"
#include "../Modules/Addons/AccuracyEffect.h"
#include "../Modules/Addons/ServerMemoryPatch.h"
#include "../Modules/Addons/ChatLog.h"
#include "../Modules/Addons/EnchantChance.h"
#include "../Modules/Addons/LootExchange.h"
#include "../Modules/Addons/BonusStart.h"
#include "../Modules/Addons/DefenceFormula.h"
#include "../Modules/Addons/GMCommands.h"
#include "../Modules/Addons/StoneHP.h"
#include "../Modules/Addons/PvpPotion.h"
#include "../Modules/Addons/MauExp.h"
#include "../Modules/Addons/Advert.h"
#include "../Modules/Addons/RadiusDropLoot.h"
#include "../Modules/Addons/ReplaceLootName.h"
#include "../Modules/Addons/VariousSettings.h"

namespace NexusProtection
{
    namespace Core
    {
        CModuleManager::CModuleManager()
        {
        }

        CModuleManager::~CModuleManager()
        {
        }

        void CModuleManager::Initialize()
        {
            RegisterEmbeddedModules();
        }

        void CModuleManager::LoadAllModules()
        {
            std::lock_guard<std::mutex> lock(m_modulesMutex);
            for (auto& [name, module] : m_modules)
            {
                try
                {
                    module->load();
                }
                catch (const std::exception& e)
                {
                    // Log error but continue with other modules
                    std::string error = "Failed to load module '" + name + "': " + e.what();
                    OutputDebugStringA(error.c_str());
                }
            }
        }

        void CModuleManager::UnloadAllModules()
        {
            std::lock_guard<std::mutex> lock(m_modulesMutex);
            for (auto& [name, module] : m_modules)
            {
                try
                {
                    module->unload();
                }
                catch (...)
                {
                    // Ignore errors during unload
                }
            }
        }

        void CModuleManager::OnZoneStart()
        {
            std::lock_guard<std::mutex> lock(m_modulesMutex);
            for (auto& [name, module] : m_modules)
            {
                try
                {
                    module->zone_start();
                }
                catch (const std::exception& e)
                {
                    std::string error = "Error in zone_start for module '" + name + "': " + e.what();
                    OutputDebugStringA(error.c_str());
                }
            }
        }

        void CModuleManager::ExecuteLoop()
        {
            std::lock_guard<std::mutex> lock(m_modulesMutex);
            for (auto& [name, module] : m_modules)
            {
                try
                {
                    module->loop();
                }
                catch (const std::exception& e)
                {
                    std::string error = "Error in loop for module '" + name + "': " + e.what();
                    OutputDebugStringA(error.c_str());
                }
            }
        }

        void CModuleManager::ConfigureModules(const rapidjson::Value& config)
        {
            std::lock_guard<std::mutex> lock(m_modulesMutex);
            
            if (!config.HasMember("modules") || !config["modules"].IsArray())
                return;

            for (const auto& moduleConfig : config["modules"].GetArray())
            {
                if (!moduleConfig.HasMember("name") || !moduleConfig.HasMember("config"))
                    continue;

                std::string moduleName = moduleConfig["name"].GetString();
                auto it = m_modules.find(moduleName);
                if (it != m_modules.end())
                {
                    try
                    {
                        it->second->configure(moduleConfig["config"]);
                    }
                    catch (const std::exception& e)
                    {
                        std::string error = "Failed to configure module '" + moduleName + "': " + e.what();
                        OutputDebugStringA(error.c_str());
                    }
                }
            }
        }

        void CModuleManager::RegisterModule(std::shared_ptr<NexusProtection::Module::IModule> module)
        {
            if (!module)
                return;

            std::lock_guard<std::mutex> lock(m_modulesMutex);
            std::string name = module->get_name();
            m_modules[name] = module;
        }

        void CModuleManager::RegisterEmbeddedModules()
        {
            RegisterCoreFixes();
            RegisterPlayerExtensions();
            RegisterAddonModules();
        }

        void CModuleManager::RegisterCoreFixes()
        {
            // Register core protection modules
            RegisterModule(std::make_shared<Modules::CoreFixes::CPlayer>());
            RegisterModule(std::make_shared<Modules::CoreFixes::CAttackSystem>());
            RegisterModule(std::make_shared<Modules::CoreFixes::CNetworkEx>());
            RegisterModule(std::make_shared<Modules::CoreFixes::CCheatCommand>());
            RegisterModule(std::make_shared<Modules::CoreFixes::CCrashDump>());
            RegisterModule(std::make_shared<Modules::CoreFixes::CPerformanceMonitor>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CMainThread>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CPotionMgr>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CGuild>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CVote>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CStore>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CPerformance>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CPostSystem>());
            RegisterModule(std::make_shared<NexusPro::CoreFixes::CUnmannedTrader>());
        }

        void CModuleManager::RegisterPlayerExtensions()
        {
            // Register player extension modules
            RegisterModule(std::make_shared<NexusPro::PlayerExtensions::CPlayerEx>());
            RegisterModule(std::make_shared<NexusPro::PlayerExtensions::CPlayerMoveSystem>());
            RegisterModule(std::make_shared<NexusPro::PlayerExtensions::CPlayerTrade>());
        }

        void CModuleManager::RegisterAddonModules()
        {
            // Register addon modules
            RegisterModule(std::make_shared<Modules::Addons::CAccuracyEffect>());
            RegisterModule(std::make_shared<Modules::Addons::CServerMemoryPatch>());
            RegisterModule(std::make_shared<Modules::Addons::CChatLog>());
            RegisterModule(std::make_shared<Modules::Addons::CEnchantChance>());
            RegisterModule(std::make_shared<Modules::Addons::CLootExchange>());
            RegisterModule(std::make_shared<Modules::Addons::CBonusStart>());
            RegisterModule(std::make_shared<Modules::Addons::CDefenceFormula>());
            RegisterModule(std::make_shared<Modules::Addons::CGMCommands>());
            RegisterModule(std::make_shared<Modules::Addons::CStoneHP>());
            RegisterModule(std::make_shared<Modules::Addons::CPvpPotion>());
            RegisterModule(std::make_shared<Modules::Addons::CMauExp>());
            RegisterModule(std::make_shared<Modules::Addons::CAdvert>());
            RegisterModule(std::make_shared<Modules::Addons::CRadiusDropLoot>());
            RegisterModule(std::make_shared<Modules::Addons::CReplaceLootName>());
            RegisterModule(std::make_shared<Modules::Addons::CVariousSettings>());

            // All addon modules have been registered
            // RegisterModule(std::make_shared<Modules::Addons::CRadiusDropLoot>());
            // RegisterModule(std::make_shared<Modules::Addons::CReplaceLootName>());
            // RegisterModule(std::make_shared<Modules::Addons::CVariousSettings>());
        }
    }
}
