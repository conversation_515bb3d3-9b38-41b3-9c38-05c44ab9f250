#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CPlayerInfo.hpp"
#include <chrono>
#include <unordered_map>
#include <mutex>

namespace NexusPro
{
    namespace PlayerExtensions
    {
        struct PlayerMovementData
        {
            float lastPosition[3]{ 0.0f, 0.0f, 0.0f };
            std::chrono::steady_clock::time_point lastMoveTime;
            float totalDistance{ 0.0f };
            uint32_t moveCount{ 0 };
            bool isValidating{ true };
        };

        class CPlayerMoveSystem : public IModuleInterface, public SingletonHelper::CSingleton<CPlayerMoveSystem>
        {
        public:
            CPlayerMoveSystem() = default;
            virtual ~CPlayerMoveSystem() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "PlayerMoveSystem"; }

            // Configuration
            void Configure(bool enableMoveValidation, bool enableSpeedCheck, bool enablePositionCheck,
                          float maxMoveDistance, float maxMoveSpeed, bool logMovement);

            // Player management
            void InitializePlayer(ATF::CPlayer* pPlayer);
            void CleanupPlayer(ATF::CPlayer* pPlayer);

            // Movement validation
            bool ValidateMovement(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget = nullptr);
            bool CheckMoveSpeed(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget);
            bool CheckMoveDistance(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget);
            bool CheckPositionValidity(ATF::CPlayer* pPlayer, float* pfPosition);

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnableMoveValidation{ true };
            bool m_bEnableSpeedCheck{ true };
            bool m_bEnablePositionCheck{ true };
            float m_maxMoveDistance{ 1000.0f };
            float m_maxMoveSpeed{ 100.0f };
            bool m_bLogMovement{ false };

            // Player movement data
            std::unordered_map<uint32_t, PlayerMovementData> m_playerMovementData;
            mutable std::mutex m_movementDataMutex;

        private:
            // Hook functions
            static void WINAPIV pc_MoveNext(
                ATF::CPlayer* pPlayer,
                char byMoveType,
                float* pfCur,
                float* pfTar,
                char byDirect,
                ATF::Info::CPlayerpc_MoveNext1793_ptr next);

            static void WINAPIV pc_RealMovPos(
                ATF::CPlayer* pPlayer,
                float* pfCur,
                ATF::Info::CPlayerpc_RealMovPos1879_ptr next);

            static void WINAPIV pc_MoveStop(
                ATF::CPlayer* pPlayer,
                float* pfCur,
                ATF::Info::CPlayerpc_MoveStop1797_ptr next);

        private:
            // Helper functions
            float CalculateDistance(float* pos1, float* pos2) const;
            float CalculateSpeed(float distance, std::chrono::milliseconds timeDiff) const;
            void UpdatePlayerMovementData(ATF::CPlayer* pPlayer, float* pfPosition);

        private:
            static CPlayerMoveSystem* s_instance;
        };
    }
}
