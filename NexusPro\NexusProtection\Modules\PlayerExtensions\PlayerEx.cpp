#include "pch.h"
#include "PlayerEx.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"
#include <algorithm>

namespace NexusPro
{
    namespace PlayerExtensions
    {
        CPlayerEx* CPlayerEx::s_instance = nullptr;

        bool CPlayerEx::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableSetItemFix = configManager.GetModuleConfigBool("playerext.player_ex", "Features", "enable_set_item_fix", true);
            m_bEnableAttackDelayFix = configManager.GetModuleConfigBool("playerext.player_ex", "Features", "enable_attack_delay_fix", true);
            m_bEnablePvpTracking = configManager.GetModuleConfigBool("playerext.player_ex", "Features", "enable_pvp_tracking", true);
            m_setItemUpdateInterval = configManager.GetModuleConfigInt("playerext.player_ex", "Timing", "set_item_update_interval", 10000);
            m_pvpCleanupInterval = configManager.GetModuleConfigInt("playerext.player_ex", "Timing", "pvp_cleanup_interval", 300000);
            m_bLogPlayerActions = configManager.GetModuleConfigBool("playerext.player_ex", "Logging", "log_player_actions", false);

            m_bInitialized = true;

            LOG_INFO("PlayerEx", "Player extensions initialized - Set Item Fix: " + std::to_string(m_bEnableSetItemFix) +
                     ", Attack Delay Fix: " + std::to_string(m_bEnableAttackDelayFix) +
                     ", PvP Tracking: " + std::to_string(m_bEnablePvpTracking));

            return true;
        }

        void CPlayerEx::Shutdown()
        {
            if (!m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            m_playerSetItems.clear();
            m_playerAttackDelays.clear();
            m_playerPvpInfo.clear();
            m_playerSetItemUpdate.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("PlayerEx", "Player extensions shutdown");
        }

        void CPlayerEx::Configure(bool enableSetItemFix, bool enableAttackDelayFix, bool enablePvpTracking,
                                 uint32_t setItemUpdateInterval, uint32_t pvpCleanupInterval)
        {
            m_bEnableSetItemFix = enableSetItemFix;
            m_bEnableAttackDelayFix = enableAttackDelayFix;
            m_bEnablePvpTracking = enablePvpTracking;
            m_setItemUpdateInterval = setItemUpdateInterval;
            m_pvpCleanupInterval = pvpCleanupInterval;
        }

        void CPlayerEx::InitializePlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            // Initialize player data structures
            m_playerSetItems[playerSerial] = detail::ContainerSetItemInfo_t{};
            m_playerAttackDelays[playerSerial] = detail::AttackDelay{};
            m_playerPvpInfo[playerSerial] = detail::PvpKillerInfo{};
            m_playerSetItemUpdate[playerSerial] = std::chrono::steady_clock::now();

            if (m_bLogPlayerActions)
            {
                LOG_DEBUG("PlayerEx", "Player initialized: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void CPlayerEx::CleanupPlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            // Remove player data
            m_playerSetItems.erase(playerSerial);
            m_playerAttackDelays.erase(playerSerial);
            m_playerPvpInfo.erase(playerSerial);
            m_playerSetItemUpdate.erase(playerSerial);

            if (m_bLogPlayerActions)
            {
                LOG_DEBUG("PlayerEx", "Player cleaned up: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void CPlayerEx::UpdatePlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized || !pPlayer->m_bLoad || !pPlayer->m_bOper)
                return;

            // Update set items if enabled and interval has passed
            if (m_bEnableSetItemFix)
            {
                auto now = std::chrono::steady_clock::now();
                auto lastUpdate = m_playerSetItemUpdate[pPlayer->m_dwSerial];
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count();

                if (elapsed >= m_setItemUpdateInterval)
                {
                    UpdateSetItem(pPlayer);
                    SendSetItemInfo(pPlayer);
                    m_playerSetItemUpdate[pPlayer->m_dwSerial] = now;
                }
            }

            // Clean PvP killer list periodically
            if (m_bEnablePvpTracking)
            {
                CleanSerialKillerList(pPlayer);
            }
        }

        void CPlayerEx::UpdateSetItem(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bEnableSetItemFix)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            detail::ContainerSetItemInfo_t& setInfo = m_playerSetItems[playerSerial];
            setInfo.clear();

            DetectActiveSetEffects(pPlayer, setInfo);

            if (m_bLogPlayerActions && !setInfo.empty())
            {
                LOG_DEBUG("PlayerEx", "Set items updated for player: " + std::string(pPlayer->m_wszPlayerName) +
                         ", Active sets: " + std::to_string(setInfo.size()));
            }
        }

        void CPlayerEx::SendSetItemInfo(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bEnableSetItemFix)
                return;

            // Don't send during siege mode or while riding
            if (pPlayer->IsSiegeMode() || pPlayer->IsRidingUnit())
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            const auto& setInfo = m_playerSetItems[playerSerial];
            for (const auto& set : setInfo)
            {
                pPlayer->SendMsg_SetItemCheckResult(8, set.info.dwSetItem, set.info.bySetEffectNum);
            }
        }

        std::chrono::milliseconds CPlayerEx::AdjustDelayValue(std::chrono::milliseconds msDelay) const
        {
            static std::chrono::milliseconds msCollision(50);
            if (msDelay >= msCollision)
                return msDelay - msCollision;
            return msDelay;
        }

        bool CPlayerEx::IsDelayExpired(const std::chrono::steady_clock::time_point& delayTime) const
        {
            return std::chrono::steady_clock::now() >= delayTime;
        }

        void CPlayerEx::SetDelayTime(std::chrono::steady_clock::time_point& delayTime, std::chrono::milliseconds msDelay)
        {
            delayTime = std::chrono::steady_clock::now() + AdjustDelayValue(msDelay);
        }

        bool CPlayerEx::CheckUnitAttackDelay(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return true;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto it = m_playerAttackDelays.find(pPlayer->m_dwSerial);
            if (it == m_playerAttackDelays.end())
                return true;

            return IsDelayExpired(it->second.unit);
        }

        bool CPlayerEx::CheckSiegeAttackDelay(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return true;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto it = m_playerAttackDelays.find(pPlayer->m_dwSerial);
            if (it == m_playerAttackDelays.end())
                return true;

            return IsDelayExpired(it->second.siege);
        }

        bool CPlayerEx::CheckNormalAttackDelay(ATF::CPlayer* pPlayer) const
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return true;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto it = m_playerAttackDelays.find(pPlayer->m_dwSerial);
            if (it == m_playerAttackDelays.end())
                return true;

            return IsDelayExpired(it->second.normal);
        }

        bool CPlayerEx::CheckForceAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub) const
        {
            if (!pPlayer || !m_bEnableAttackDelayFix || nCode < 0 || nCode >= 10 || nSub < 0 || nSub >= 10)
                return true;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto it = m_playerAttackDelays.find(pPlayer->m_dwSerial);
            if (it == m_playerAttackDelays.end())
                return true;

            return IsDelayExpired(it->second.force[nCode][nSub]);
        }

        bool CPlayerEx::CheckSkillAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, int indx) const
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return true;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto it = m_playerAttackDelays.find(pPlayer->m_dwSerial);
            if (it == m_playerAttackDelays.end())
                return true;

            if (nCode == -1)
            {
                if (nSub < 0 || nSub >= 10 || indx < 0 || indx >= 10)
                    return true;
                return IsDelayExpired(it->second.class_skill[nSub][indx]);
            }
            else
            {
                if (nCode < 0 || nCode >= 10 || nSub < 0 || nSub >= 10)
                    return true;
                return IsDelayExpired(it->second.skill[nCode][nSub]);
            }
        }

        void CPlayerEx::SetUnitAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay)
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].unit, msDelay);
        }

        void CPlayerEx::SetSiegeAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay)
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].siege, msDelay);
        }

        void CPlayerEx::SetNormalAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay)
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].normal, msDelay);
        }

        void CPlayerEx::SetForceAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, std::chrono::milliseconds msDelay)
        {
            if (!pPlayer || !m_bEnableAttackDelayFix || nCode < 0 || nCode >= 10 || nSub < 0 || nSub >= 10)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].force[nCode][nSub], msDelay);
        }

        void CPlayerEx::SetSkillAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, int indx, std::chrono::milliseconds msDelay)
        {
            if (!pPlayer || !m_bEnableAttackDelayFix)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);

            if (nCode == -1)
            {
                if (nSub >= 0 && nSub < 10 && indx >= 0 && indx < 10)
                    SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].class_skill[nSub][indx], msDelay);
            }
            else
            {
                if (nCode >= 0 && nCode < 10 && nSub >= 0 && nSub < 10)
                    SetDelayTime(m_playerAttackDelays[pPlayer->m_dwSerial].skill[nCode][nSub], msDelay);
            }
        }

        bool CPlayerEx::AlreadyKilled(ATF::CPlayer* pPlayer, uint32_t dwKillerSerial)
        {
            if (!pPlayer || !m_bEnablePvpTracking)
                return false;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto& pvpInfo = m_playerPvpInfo[pPlayer->m_dwSerial];
            std::lock_guard<std::mutex> pvpLock(pvpInfo.mtx);

            return pvpInfo.killerList.find(dwKillerSerial) != pvpInfo.killerList.end();
        }

        bool CPlayerEx::PushSerialKiller(ATF::CPlayer* pPlayer, uint32_t dwKillerSerial)
        {
            if (!pPlayer || !m_bEnablePvpTracking || dwKillerSerial == 0)
                return false;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto& pvpInfo = m_playerPvpInfo[pPlayer->m_dwSerial];
            std::lock_guard<std::mutex> pvpLock(pvpInfo.mtx);

            auto result = pvpInfo.killerList.insert(dwKillerSerial);

            if (m_bLogPlayerActions && result.second)
            {
                LOG_DEBUG("PlayerEx", "PvP killer added for player: " + std::string(pPlayer->m_wszPlayerName) +
                         ", Killer Serial: " + std::to_string(dwKillerSerial));
            }

            return result.second;
        }

        void CPlayerEx::CleanSerialKillerList(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bEnablePvpTracking)
                return;

            std::lock_guard<std::mutex> lock(m_playerDataMutex);
            auto& pvpInfo = m_playerPvpInfo[pPlayer->m_dwSerial];
            std::lock_guard<std::mutex> pvpLock(pvpInfo.mtx);

            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - pvpInfo.lastCleanup).count();

            if (elapsed >= m_pvpCleanupInterval)
            {
                size_t oldSize = pvpInfo.killerList.size();
                pvpInfo.killerList.clear();
                pvpInfo.lastCleanup = now;

                if (m_bLogPlayerActions && oldSize > 0)
                {
                    LOG_DEBUG("PlayerEx", "PvP killer list cleaned for player: " + std::string(pPlayer->m_wszPlayerName) +
                             ", Removed: " + std::to_string(oldSize) + " entries");
                }
            }
        }

        bool CPlayerEx::CheckMove(ATF::CPlayer* pPlayer, float* pfTarget) const
        {
            if (!pPlayer || !pfTarget)
                return false;

            // Basic movement validation
            // Add specific movement checks based on server requirements
            return true;
        }

        void CPlayerEx::DetectActiveSetEffects(ATF::CPlayer* pPlayer, detail::ContainerSetItemInfo_t& setInfo)
        {
            if (!pPlayer)
                return;

            // This would implement the complex set item detection logic
            // For now, we'll provide a basic implementation
            // The full implementation would require access to ATF::CSUItemSystem

            if (m_bLogPlayerActions)
            {
                LOG_DEBUG("PlayerEx", "Detecting set effects for player: " + std::string(pPlayer->m_wszPlayerName));
            }

            // TODO: Implement full set item detection logic
            // This requires integration with the ATF item system
        }

        bool CPlayerEx::ValidateSetItemEquipment(ATF::CPlayer* pPlayer, uint32_t dwSetIndex) const
        {
            if (!pPlayer)
                return false;

            // Basic validation - would be expanded based on server requirements
            return true;
        }
    }
}
