[General]
; Main Thread Performance Optimization Module
; This module optimizes the main server thread by implementing priority-based task scheduling
enabled=true

[Performance]
; Priority task intervals in milliseconds
; Lower values = higher frequency execution
; Higher values = lower frequency execution

; Priority 1: Critical systems (player updates, network processing)
priority_1=100

; Priority 2: Important systems (combat, movement)
priority_2=500

; Priority 3: Regular systems (guild updates, chat processing)
priority_3=1000

; Priority 4: Background systems (statistics, logging)
priority_4=2000

; Priority 5: Maintenance tasks (cleanup, maintenance)
priority_5=5000

[Advanced]
; Enable detailed performance logging
enable_performance_logging=false

; Maximum task execution time warning threshold (milliseconds)
max_task_execution_time=50

[Description]
; MainThread module provides:
; - Priority-based task scheduling for optimal server performance
; - Configurable execution intervals for different system priorities
; - Performance monitoring and optimization
; - Force close detection and handling
; - Main thread loop optimization
