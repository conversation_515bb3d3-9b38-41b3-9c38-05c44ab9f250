<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>NexusProtection</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(ProjectDir);$(ProjectDir)Common;$(ProjectDir)..\Dependencies\rapidjson\include;$(ProjectDir)..\Dependencies\MinHook\include;$(ProjectDir)..\Dependencies\P7ClientLib\Headers;$(ProjectDir)..\Dependencies\NexusProtectionLib;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)..\Dependencies\MinHook\lib\x64;$(ProjectDir)..\Dependencies\P7ClientLib\Binaries\x64;$(ProjectDir)..\Dependencies\NexusProtectionLib\lib\x64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(ProjectDir)Common;$(ProjectDir)..\Dependencies\rapidjson\include;$(ProjectDir)..\Dependencies\MinHook\include;$(ProjectDir)..\Dependencies\P7ClientLib\Headers;$(ProjectDir)..\Dependencies\NexusProtectionLib;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)..\Dependencies\MinHook\lib\x64;$(ProjectDir)..\Dependencies\P7ClientLib\Binaries\x64;$(ProjectDir)..\Dependencies\NexusProtectionLib\lib\x64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;NEXUSPROTECTION_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)Common;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>libMinHook-x64-v143-md.lib;P7_x64.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;NEXUSPROTECTION_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)Common;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>libMinHook-x64-v143-md.lib;P7_x64.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="framework.h" />
    <ClInclude Include="pch.h" />
    <!-- Core System Headers -->
    <ClInclude Include="Core\NexusCore.h" />
    <ClInclude Include="Core\ConfigManager.h" />
    <ClInclude Include="Core\ModuleManager.h" />
    <ClInclude Include="Core\Logger.h" />
    <!-- Common Headers -->
    <ClInclude Include="Common\Interfaces\ModuleInterface.h" />
    <ClInclude Include="Common\Helpers\ModuleHook.h" />
    <ClInclude Include="Common\Helpers\AntiCheat.h" />
    <ClInclude Include="Common\ETypes.h" />
    <!-- ATF Headers -->
    <ClInclude Include="Common\ATF\common.h" />
    <ClInclude Include="Common\ATF\CPlayer.hpp" />
    <ClInclude Include="Common\ATF\CCharacter.hpp" />
    <ClInclude Include="Common\ATF\CAnimus.hpp" />
    <ClInclude Include="Common\ATF\CItem.hpp" />
    <ClInclude Include="Common\ATF\CServer.hpp" />
    <ClInclude Include="Common\ATF\CGuardTower.hpp" />
    <ClInclude Include="Common\ATF\Info.hpp" />
    <ClInclude Include="Common\ATF\_attack_param.hpp" />
    <ClInclude Include="Common\ATF\_skill_fld.hpp" />
    <ClInclude Include="Common\ATF\_force_fld.hpp" />
    <ClInclude Include="Common\ATF\_STORAGE_LIST.hpp" />
    <ClInclude Include="Common\ATF\_BulletItem_fld.hpp" />
    <ClInclude Include="Common\ATF\_UnitPart_fld.hpp" />
    <ClInclude Include="Common\ATF\_UnitBullet_fld.hpp" />
    <ClInclude Include="Common\ATF\_unit_bullet_param.hpp" />
    <ClInclude Include="Common\ATF\_CHRID.hpp" />
    <ClInclude Include="Common\ATF\_DTRADE_PARAM.hpp" />
    <ClInclude Include="Common\ATF\_STORAGE_POS_INDIV.hpp" />
    <ClInclude Include="Common\ATF\_ITEM_SERIAL.hpp" />
    <ClInclude Include="Common\ATF\_tower_create_setdata.hpp" />
    <ClInclude Include="Common\ATF\_object_id.hpp" />
    <!-- Core Protection Module Headers -->
    <ClInclude Include="Modules\CoreFixes\AttackSystem.h" />
    <ClInclude Include="Modules\CoreFixes\NetworkEx.h" />
    <ClInclude Include="Modules\CoreFixes\CheatCommand.h" />
    <ClInclude Include="Modules\CoreFixes\CrashDump.h" />
    <ClInclude Include="Modules\CoreFixes\PerformanceMonitor.h" />
    <ClInclude Include="Modules\CoreFixes\Player.h" />
    <ClInclude Include="Modules\CoreFixes\MainThread.h" />
    <ClInclude Include="Modules\CoreFixes\PotionMgr.h" />
    <ClInclude Include="Modules\CoreFixes\Guild.h" />
    <ClInclude Include="Modules\CoreFixes\Vote.h" />
    <ClInclude Include="Modules\CoreFixes\Store.h" />
    <!-- Player Extension Module Headers -->
    <ClInclude Include="Modules\PlayerExtensions\PlayerEx.h" />
    <ClInclude Include="Modules\PlayerExtensions\PlayerMoveSystem.h" />
    <ClInclude Include="Modules\PlayerExtensions\PlayerTrade.h" />
    <!-- Addon Module Headers -->
    <ClInclude Include="Modules\Addons\AccuracyEffect.h" />
    <ClInclude Include="Modules\Addons\ServerMemoryPatch.h" />
    <ClInclude Include="Modules\Addons\ChatLog.h" />
    <ClInclude Include="Modules\Addons\EnchantChance.h" />
    <ClInclude Include="Modules\Addons\LootExchange.h" />
    <ClInclude Include="Modules\Addons\BonusStart.h" />
    <ClInclude Include="Modules\Addons\DefenceFormula.h" />
    <ClInclude Include="Modules\Addons\GMCommands.h" />
    <ClInclude Include="Modules\Addons\StoneHP.h" />
    <ClInclude Include="Modules\Addons\PvpPotion.h" />
    <ClInclude Include="Modules\Addons\MauExp.h" />
    <ClInclude Include="Modules\Addons\Advert.h" />
    <ClInclude Include="Modules\Addons\RadiusDropLoot.h" />
    <ClInclude Include="Modules\Addons\ReplaceLootName.h" />
    <ClInclude Include="Modules\Addons\VariousSettings.h" />
    <!-- Template Headers -->
    <ClInclude Include="Templates\ModuleTemplate.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <!-- Core System Sources -->
    <ClCompile Include="Core\NexusCore.cpp" />
    <ClCompile Include="Core\ConfigManager.cpp" />
    <ClCompile Include="Core\ModuleManager.cpp" />
    <ClCompile Include="Core\Logger.cpp" />
    <!-- Common Sources -->
    <ClCompile Include="Common\Helpers\ModuleHook.cpp" />
    <ClCompile Include="Common\Helpers\AntiCheat.cpp" />
    <!-- Core Protection Module Sources -->
    <ClCompile Include="Modules\CoreFixes\AttackSystem.cpp" />
    <ClCompile Include="Modules\CoreFixes\NetworkEx.cpp" />
    <ClCompile Include="Modules\CoreFixes\CheatCommand.cpp" />
    <ClCompile Include="Modules\CoreFixes\CrashDump.cpp" />
    <ClCompile Include="Modules\CoreFixes\PerformanceMonitor.cpp" />
    <ClCompile Include="Modules\CoreFixes\Player.cpp" />
    <ClCompile Include="Modules\CoreFixes\MainThread.cpp" />
    <ClCompile Include="Modules\CoreFixes\PotionMgr.cpp" />
    <ClCompile Include="Modules\CoreFixes\Guild.cpp" />
    <ClCompile Include="Modules\CoreFixes\Vote.cpp" />
    <ClCompile Include="Modules\CoreFixes\Store.cpp" />
    <!-- Player Extension Module Sources -->
    <ClCompile Include="Modules\PlayerExtensions\PlayerEx.cpp" />
    <ClCompile Include="Modules\PlayerExtensions\PlayerMoveSystem.cpp" />
    <ClCompile Include="Modules\PlayerExtensions\PlayerTrade.cpp" />
    <!-- Addon Module Sources -->
    <ClCompile Include="Modules\Addons\AccuracyEffect.cpp" />
    <ClCompile Include="Modules\Addons\ServerMemoryPatch.cpp" />
    <ClCompile Include="Modules\Addons\ChatLog.cpp" />
    <ClCompile Include="Modules\Addons\EnchantChance.cpp" />
    <ClCompile Include="Modules\Addons\LootExchange.cpp" />
    <ClCompile Include="Modules\Addons\BonusStart.cpp" />
    <ClCompile Include="Modules\Addons\DefenceFormula.cpp" />
    <ClCompile Include="Modules\Addons\GMCommands.cpp" />
    <ClCompile Include="Modules\Addons\StoneHP.cpp" />
    <ClCompile Include="Modules\Addons\PvpPotion.cpp" />
    <ClCompile Include="Modules\Addons\MauExp.cpp" />
    <ClCompile Include="Modules\Addons\Advert.cpp" />
    <ClCompile Include="Modules\Addons\RadiusDropLoot.cpp" />
    <ClCompile Include="Modules\Addons\ReplaceLootName.cpp" />
    <ClCompile Include="Modules\Addons\VariousSettings.cpp" />
    <!-- Template Sources -->
    <ClCompile Include="Templates\ModuleTemplate.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
