#include "pch.h"
#include "UnmannedTrader.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CUnmannedTrader* CUnmannedTrader::s_instance = nullptr;

        bool CUnmannedTrader::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableTraderFixes = configManager.GetModuleConfigBool("corefix.unmanned_trader", "Features", "enable_trader_fixes", true);
            m_bEnableValidation = configManager.GetModuleConfigBool("corefix.unmanned_trader", "Validation", "enable_validation", true);
            m_bEnableMetrics = configManager.GetModuleConfigBool("corefix.unmanned_trader", "Monitoring", "enable_metrics", false);
            m_maxTransactionsPerHour = configManager.GetModuleConfigInt("corefix.unmanned_trader", "Limits", "max_transactions_per_hour", 100);
            m_maxGoldPerTransaction = configManager.GetModuleConfigInt("corefix.unmanned_trader", "Limits", "max_gold_per_transaction", 10000000);
            m_bLogTraderActivity = configManager.GetModuleConfigBool("corefix.unmanned_trader", "Logging", "log_trader_activity", false);

            // Hook unmanned trader functions
            if (m_bEnableTraderFixes)
            {
                enable_hook(&ATF::CUnmannedTraderUserInfoTable::CompleteBuy, &CUnmannedTrader::CompleteBuy);
                enable_hook(&ATF::CUnmannedTraderUserInfoTable::CompleteReRegist, &CUnmannedTrader::CompleteReRegist);
                enable_hook(&ATF::CUnmannedTraderUserInfo::ReRegist, &CUnmannedTrader::ReRegist);
                enable_hook(&ATF::CUnmannedTraderController::UpdateReRegist, &CUnmannedTrader::UpdateReRegist);
                enable_hook(&ATF::CUnmannedTraderUserInfo::NotifyRegistItem, &CUnmannedTrader::NotifyRegistItem);
            }

            m_bInitialized = true;

            LOG_INFO("UnmannedTrader", "Unmanned trader fixes initialized - Trader Fixes: " + std::to_string(m_bEnableTraderFixes) +
                     ", Validation: " + std::to_string(m_bEnableValidation) +
                     ", Metrics: " + std::to_string(m_bEnableMetrics) +
                     ", Max Transactions/Hour: " + std::to_string(m_maxTransactionsPerHour) +
                     ", Max Gold: " + std::to_string(m_maxGoldPerTransaction));

            return true;
        }

        void CUnmannedTrader::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();

            std::lock_guard<std::mutex> lock(m_traderDataMutex);
            m_traderData.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("UnmannedTrader", "Unmanned trader fixes shutdown");
        }

        void CUnmannedTrader::Configure(bool enableTraderFixes, bool enableValidation, bool enableMetrics,
                                       uint32_t maxTransactionsPerHour, uint64_t maxGoldPerTransaction, bool logTraderActivity)
        {
            m_bEnableTraderFixes = enableTraderFixes;
            m_bEnableValidation = enableValidation;
            m_bEnableMetrics = enableMetrics;
            m_maxTransactionsPerHour = maxTransactionsPerHour;
            m_maxGoldPerTransaction = maxGoldPerTransaction;
            m_bLogTraderActivity = logTraderActivity;
        }

        bool CUnmannedTrader::ValidateTraderTransaction(ATF::CPlayer* pPlayer, uint32_t dwGold)
        {
            if (!pPlayer || !m_bEnableValidation)
                return true;

            // Validate gold amount
            if (!ValidateGoldAmount(dwGold))
                return false;

            // Validate transaction limits
            if (!ValidateTransactionLimits(pPlayer))
                return false;

            return true;
        }

        bool CUnmannedTrader::ValidateTraderRegistration(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bEnableValidation)
                return true;

            // Basic registration validation
            // Additional checks can be added here
            return true;
        }

        void CUnmannedTrader::UpdateTraderMetrics(const std::string& operation, uint32_t count, uint64_t gold)
        {
            if (!m_bEnableMetrics)
                return;

            std::lock_guard<std::mutex> lock(m_traderDataMutex);

            if (operation == "buy_success")
            {
                m_metrics.successfulBuys += count;
                m_metrics.totalGoldTransferred += gold;
            }
            else if (operation == "buy_failed")
            {
                m_metrics.failedBuys += count;
            }
            else if (operation == "registration")
            {
                m_metrics.totalRegistrations += count;
            }
            else if (operation == "reregistration")
            {
                m_metrics.totalReRegistrations += count;
            }

            m_metrics.totalTransactions += count;
        }

        bool CUnmannedTrader::ValidateTransactionLimits(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer)
                return false;

            std::lock_guard<std::mutex> lock(m_traderDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;
            auto& data = m_traderData[playerSerial];

            auto now = std::chrono::steady_clock::now();
            auto hourAgo = now - std::chrono::hours(1);

            // Reset counter if more than an hour has passed
            if (data.lastTransaction < hourAgo)
            {
                data.transactionCount = 0;
            }

            // Check transaction limit
            if (data.transactionCount >= m_maxTransactionsPerHour)
            {
                if (m_bLogTraderActivity)
                {
                    LOG_WARNING("UnmannedTrader", "Transaction limit exceeded - Player: " + std::string(pPlayer->m_wszPlayerName) +
                               ", Count: " + std::to_string(data.transactionCount) + ", Limit: " + std::to_string(m_maxTransactionsPerHour));
                }
                return false;
            }

            // Update transaction data
            data.transactionCount++;
            data.lastTransaction = now;
            data.playerSerial = playerSerial;

            return true;
        }

        bool CUnmannedTrader::ValidateGoldAmount(uint64_t dwGold)
        {
            if (dwGold > m_maxGoldPerTransaction)
            {
                if (m_bLogTraderActivity)
                {
                    LOG_WARNING("UnmannedTrader", "Gold limit exceeded - Amount: " + std::to_string(dwGold) +
                               ", Limit: " + std::to_string(m_maxGoldPerTransaction));
                }
                return false;
            }
            return true;
        }

        void CUnmannedTrader::ProcessBuyTransaction(ATF::CUnmannedTraderUserInfoTable* pObj, char* pLoadData,
                                                   ATF::CUnmannedTraderTradeInfo* pkTradeInfo)
        {
            if (!pObj || !pLoadData)
                return;

            ATF::_qry_case_unmandtrader_buy_update_wait* pkQuery = (ATF::_qry_case_unmandtrader_buy_update_wait*)pLoadData;
            
            ATF::CPlayer* ppkBuyPlayer = nullptr;
            ATF::CUnmannedTraderUserInfo* ppkBuyUser = nullptr;

            if (!pObj->SubCompleteBuyFindBuyer(pkQuery, &ppkBuyUser, &ppkBuyPlayer))
                return;

            if (!ppkBuyUser || !ppkBuyPlayer)
                return;

            // Validate transaction
            if (!ValidateTraderTransaction(ppkBuyPlayer, 0)) // Gold validation done elsewhere
            {
                UpdateTraderMetrics("buy_failed");
                return;
            }

            ppkBuyUser->ClearRequest();

            ATF::_unmannedtrader_buy_item_result_zocl Dst;
            ATF::_qry_case_unmandtrader_buy_update_complete pQryData;

            memset(&Dst, 0, sizeof(Dst));
            memset(&pQryData, 0, sizeof(pQryData));

            Dst.byRetCode = 0;
            Dst.byNum = pkQuery->byNum;

            pQryData.wInx = ppkBuyPlayer->m_ObjID.m_wIndex;
            pQryData.dwBuyer = pkQuery->dwBuyer;
            pQryData.byRace = ppkBuyPlayer->m_Param.GetRaceCode();
            pQryData.byType = pkQuery->byType;

            int nSucceeded = 0;
            uint64_t totalGold = 0;

            for (int j = 0; j < pkQuery->byNum; ++j)
            {
                int indx = pQryData.byNum;
                if (pObj->SubCompleteBuyProcBuy(
                    ppkBuyPlayer,
                    &pkQuery->List[j],
                    &Dst.List[indx],
                    &pQryData.List[indx]))
                {
                    pQryData.byNum++;
                    nSucceeded++;
                    totalGold += pkQuery->List[j].dwGold;
                }
            }

            if (nSucceeded > 0)
            {
                UpdateTraderMetrics("buy_success", nSucceeded, totalGold);

                if (m_bLogTraderActivity)
                {
                    LOG_INFO("UnmannedTrader", "Buy transaction completed - Player: " + std::string(ppkBuyPlayer->m_wszPlayerName) +
                            ", Items: " + std::to_string(nSucceeded) + ", Gold: " + std::to_string(totalGold));
                }
            }

            // Send result to player
            ppkBuyPlayer->SendMsg(&Dst, sizeof(Dst));

            // Update database if needed
            if (pQryData.byNum > 0)
            {
                ATF::Global::g_pMainThread->PushDQSUnmannedTraderBuyUpdateComplete(&pQryData);
            }
        }

        void CUnmannedTrader::ProcessReRegistration(ATF::CUnmannedTraderUserInfo* pObj, char byType,
                                                   ATF::_unmannedtrader_re_regist_request_clzo* pRequest)
        {
            if (!pObj || !pRequest)
                return;

            UpdateTraderMetrics("reregistration");

            if (m_bLogTraderActivity)
            {
                LOG_INFO("UnmannedTrader", "Re-registration processed - Type: " + std::to_string(byType));
            }
        }

        void WINAPIV CUnmannedTrader::CompleteBuy(
            ATF::CUnmannedTraderUserInfoTable* pObj,
            char byRet,
            char* pLoadData,
            ATF::CUnmannedTraderTradeInfo* pkTradeInfo,
            ATF::Info::CUnmannedTraderUserInfoTableCompleteBuy16_ptr next)
        {
            if (!pObj || !pLoadData || !s_instance)
            {
                next(pObj, byRet, pLoadData, pkTradeInfo);
                return;
            }

            if (byRet == 1)
            {
                next(pObj, byRet, pLoadData, pkTradeInfo);
                return;
            }

            // Process buy transaction with validation
            s_instance->ProcessBuyTransaction(pObj, pLoadData, pkTradeInfo);
        }

        void WINAPIV CUnmannedTrader::ReRegist(
            ATF::CUnmannedTraderUserInfo* pObj,
            char byType,
            ATF::_unmannedtrader_re_regist_request_clzo* pRequest,
            ATF::CLogFile* pkLogger,
            ATF::Info::CUnmannedTraderUserInfoReRegist110_ptr next)
        {
            if (!pObj || !pRequest || !s_instance)
            {
                next(pObj, byType, pRequest, pkLogger);
                return;
            }

            // Process re-registration
            s_instance->ProcessReRegistration(pObj, byType, pRequest);

            next(pObj, byType, pRequest, pkLogger);
        }

        bool WINAPIV CUnmannedTrader::UpdateReRegist(
            ATF::CUnmannedTraderController* pObj,
            char* pData,
            ATF::Info::CUnmannedTraderControllerUpdateReRegist108_ptr next)
        {
            if (!pObj || !pData || !s_instance)
                return next(pObj, pData);

            if (s_instance->m_bLogTraderActivity)
            {
                LOG_DEBUG("UnmannedTrader", "Update re-registration called");
            }

            return next(pObj, pData);
        }

        void WINAPIV CUnmannedTrader::CompleteReRegist(
            ATF::CUnmannedTraderUserInfoTable* pObj,
            char* pData,
            ATF::Info::CUnmannedTraderUserInfoTableCompleteReRegist22_ptr next)
        {
            if (!pObj || !pData || !s_instance)
            {
                next(pObj, pData);
                return;
            }

            if (s_instance->m_bLogTraderActivity)
            {
                LOG_DEBUG("UnmannedTrader", "Complete re-registration called");
            }

            next(pObj, pData);
        }

        void WINAPIV CUnmannedTrader::NotifyRegistItem(
            ATF::CUnmannedTraderUserInfo* pObj,
            ATF::CPlayer* pPlayer,
            ATF::_unmannedtrader_regist_item_result_zocl* pResult,
            ATF::Info::CUnmannedTraderUserInfoNotifyRegistItem112_ptr next)
        {
            if (!pObj || !pPlayer || !pResult || !s_instance)
            {
                next(pObj, pPlayer, pResult);
                return;
            }

            // Validate registration
            if (s_instance->ValidateTraderRegistration(pPlayer))
            {
                s_instance->UpdateTraderMetrics("registration");

                if (s_instance->m_bLogTraderActivity)
                {
                    LOG_INFO("UnmannedTrader", "Item registration - Player: " + std::string(pPlayer->m_wszPlayerName));
                }

                next(pObj, pPlayer, pResult);
            }
            else if (s_instance->m_bLogTraderActivity)
            {
                LOG_WARNING("UnmannedTrader", "Item registration blocked - Player: " + std::string(pPlayer->m_wszPlayerName));
            }
        }
    }
}
