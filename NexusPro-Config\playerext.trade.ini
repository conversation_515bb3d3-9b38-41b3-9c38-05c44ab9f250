[General]
; Player Trade System Module
; This module provides trade validation and anti-exploit measures
enabled=true

[Validation]
; Enable trade validation
enable_trade_validation=true

; Enable race compatibility checking
enable_race_check=true

; Enable degrade compatibility checking
enable_degrade_check=true

; Enable item validation
enable_item_validation=true

; Enable trade state validation
enable_state_validation=true

[Security]
; Enable anti-duplication measures
enable_anti_dupe=true

; Enable trade spam prevention
enable_spam_prevention=true

; Enable item verification
enable_item_verification=true

; Enable trade logging for security
enable_security_logging=true

[Restrictions]
; Block cross-race trading
block_cross_race_trading=false

; Block degrade incompatible trading
block_degrade_incompatible=true

; Block trading during combat
block_during_combat=true

; Block trading in safe zones
block_in_safe_zones=false

; Maximum trade distance
max_trade_distance=100.0

[Items]
; Enable item exchangeability checking
enable_exchangeability_check=true

; Block trading of bound items
block_bound_items=true

; Block trading of quest items
block_quest_items=true

; Block trading of unique items
block_unique_items=false

; Maximum items per trade
max_items_per_trade=20

[AntiExploit]
; Enable trade timeout
enable_trade_timeout=true

; Trade timeout duration (seconds)
trade_timeout_duration=300

; Maximum trade attempts per minute
max_trade_attempts=10

; Enable trade cooldown
enable_trade_cooldown=false

; Trade cooldown duration (seconds)
trade_cooldown_duration=30

[Performance]
; Enable trade data caching
enable_trade_caching=true

; Maximum cached trade data entries
max_cached_entries=500

; Cache cleanup interval (milliseconds)
cache_cleanup_interval=300000

[Logging]
; Enable trade logging
log_trades=false

; Log blocked trades
log_blocked_trades=true

; Log trade validation failures
log_validation_failures=true

; Log item validation failures
log_item_failures=true

; Log security violations
log_security_violations=true

[Description]
; PlayerTrade module provides:
; - Trade validation and security measures
; - Race and degrade compatibility checking
; - Item exchangeability validation
; - Anti-duplication and anti-exploit protection
; - Trade state and mode validation
; - Comprehensive trade logging and monitoring
; - Performance-optimized trade data management
; - Trade spam and abuse prevention
