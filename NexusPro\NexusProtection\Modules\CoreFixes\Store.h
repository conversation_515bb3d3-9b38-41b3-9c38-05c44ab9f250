#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CItemStoreInfo.hpp"
#include <array>

namespace NexusPro
{
    namespace CoreFixes
    {
        // Money type enumeration for price calculations
        enum class MoneyType : size_t
        {
            Dalant = 0,
            Gold = 1,
            Point = 2,
            ActPoint = 3,
            Num = 4
        };

        class CStore : public IModuleInterface, public SingletonHelper::CSingleton<CStore>
        {
        public:
            CStore() = default;
            virtual ~CStore() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "Store"; }

            // Configuration
            void Configure(bool enableOverlapFix, bool enablePriceFix, bool enableDiscountFix,
                          bool enableOverflowFix, float maxDiscountRate, bool logTransactions);

        private:
            bool m_bInitialized{ false };

            // Configuration flags
            bool m_bEnableOverlapFix{ true };
            bool m_bEnablePriceFix{ true };
            bool m_bEnableDiscountFix{ true };
            bool m_bEnableOverflowFix{ true };
            float m_maxDiscountRate{ 1.0f };
            bool m_bLogTransactions{ false };

        private:
            // Hook functions
            static char WINAPIV IsSell(
                ATF::CItemStore* pObj,
                char byOfferNum,
                ATF::_buy_offer* pOffer,
                unsigned int dwHasDalant,
                unsigned int dwHasGold,
                long double dHasPoint,
                unsigned int* dwHasActPoint,
                char* pbyActCode,
                float fDiscountRate,
                char byRace,
                char byGrade,
                ATF::Info::CItemStoreIsSell36_ptr next);

            // Helper functions
            static bool ValidateItemOverlap(ATF::CItemStore* pStore, char byOfferNum, ATF::_buy_offer* pOffer);
            static bool ValidatePriceCalculation(const std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices);
            static bool ValidateDiscountRate(float& discountRate, float maxRate);
            static void CalculatePrices(ATF::CItemStore* pStore, char byOfferNum, ATF::_buy_offer* pOffer,
                                       char byRace, std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices);
            static void ApplyDiscount(std::array<uint64_t, static_cast<size_t>(MoneyType::Num)>& prices, float discountRate);

        private:
            static CStore* s_instance;
        };
    }
}
