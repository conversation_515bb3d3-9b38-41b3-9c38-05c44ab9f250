#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CMainThreadInfo.hpp"
#include <chrono>
#include <vector>
#include <unordered_map>
#include <functional>

namespace NexusPro
{
    namespace CoreFixes
    {
        namespace detail
        {
            class CRunRule
            {
            public:
                CRunRule() : CRunRule(false) {}

                CRunRule(const std::chrono::milliseconds& interval)
                    : CRunRule(true, interval) {}

                bool runnable() const
                {
                    bool result = true;
                    if (m_bEnabledTimer)
                    {
                        auto now = std::chrono::steady_clock::now();
                        result = (now - m_lastRun) >= m_interval;
                        if (result)
                            m_lastRun = now;
                    }
                    return result;
                }

            private:
                CRunRule(bool enabled_timer, const std::chrono::milliseconds& interval = std::chrono::milliseconds(0))
                    : m_bEnabledTimer(enabled_timer)
                    , m_interval(interval)
                    , m_lastRun(std::chrono::steady_clock::now()) {}

            private:
                const bool m_bEnabledTimer;
                const std::chrono::milliseconds m_interval;
                mutable std::chrono::steady_clock::time_point m_lastRun;
            };

            class CTask
            {
            public:
                CTask(const CRunRule& rule, const std::function<void()>& impl)
                    : m_ruleRun(rule), m_fnImpl(impl) {}

                CTask(const std::function<void()>& impl)
                    : CTask(CRunRule{}, impl) {}

                void run() const
                {
                    if (m_ruleRun.runnable())
                        m_fnImpl();
                }

            private:
                const CRunRule m_ruleRun;
                const std::function<void()> m_fnImpl;
            };

            using Tasks_t = std::vector<CTask>;

            enum class priority_task
            {
                one,
                two,
                three,
                four,
                five,
                num
            };

            using PriorityTimer_t = std::unordered_map<priority_task, std::chrono::milliseconds>;
        }

        class CMainThread : public IModuleInterface, public SingletonHelper::CSingleton<CMainThread>
        {
        public:
            CMainThread() = default;
            virtual ~CMainThread() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "MainThread"; }

            // Configuration
            void Configure(uint32_t priority1, uint32_t priority2, uint32_t priority3, 
                          uint32_t priority4, uint32_t priority5);

        private:
            void MakeTasks(const detail::PriorityTimer_t& priorityTimerValue);

        private:
            detail::Tasks_t m_tasks;
            bool m_bInitialized{ false };

            // Configuration values
            uint32_t m_priority1{ 100 };
            uint32_t m_priority2{ 500 };
            uint32_t m_priority3{ 1000 };
            uint32_t m_priority4{ 2000 };
            uint32_t m_priority5{ 5000 };

        private:
            // Hook functions
            static void WINAPIV CheckForceClose(
                ATF::CMainThread* pObj,
                ATF::Info::CMainThreadCheckForceClose24_ptr next);

            static void WINAPIV OnRun(
                ATF::CMainThread* pObj,
                ATF::Info::CMainThreadOnRun130_ptr next);

        private:
            static CMainThread* s_instance;
        };
    }
}
