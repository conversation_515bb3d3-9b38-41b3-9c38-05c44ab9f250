#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CPlayer.hpp"
#include <chrono>
#include <vector>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <mutex>

namespace NexusPro
{
    namespace PlayerExtensions
    {
        namespace detail
        {
            // Set item information structure
            union SetItemInfo
            {
                struct
                {
                    uint32_t dwSetItem;
                    uint8_t bySetItemNum;
                    uint8_t bySetEffectNum;
                } info;

                uint64_t value;

                SetItemInfo() : value(0) {}

                bool operator<(const SetItemInfo& rhs) const
                {
                    return (value < rhs.value);
                }
            };

            using ContainerSetItemInfo_t = std::set<SetItemInfo>;

            // Attack delay structure
            struct AttackDelay
            {
                std::chrono::steady_clock::time_point unit;
                std::chrono::steady_clock::time_point siege;
                std::chrono::steady_clock::time_point normal;
                std::chrono::steady_clock::time_point force[10][10];
                std::chrono::steady_clock::time_point skill[10][10];
                std::chrono::steady_clock::time_point class_skill[10][10];
            };

            // PvP killer tracking
            struct PvpKillerInfo
            {
                std::set<uint32_t> killerList;
                std::chrono::steady_clock::time_point lastCleanup;
                std::mutex mtx;
            };
        }

        class CPlayerEx : public IModuleInterface, public SingletonHelper::CSingleton<CPlayerEx>
        {
        public:
            CPlayerEx() = default;
            virtual ~CPlayerEx() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "PlayerEx"; }

            // Configuration
            void Configure(bool enableSetItemFix, bool enableAttackDelayFix, bool enablePvpTracking,
                          uint32_t setItemUpdateInterval, uint32_t pvpCleanupInterval);

            // Player management
            void InitializePlayer(ATF::CPlayer* pPlayer);
            void CleanupPlayer(ATF::CPlayer* pPlayer);
            void UpdatePlayer(ATF::CPlayer* pPlayer);

            // Set item management
            void UpdateSetItem(ATF::CPlayer* pPlayer);
            void SendSetItemInfo(ATF::CPlayer* pPlayer);

            // Attack delay management
            bool CheckUnitAttackDelay(ATF::CPlayer* pPlayer) const;
            bool CheckSiegeAttackDelay(ATF::CPlayer* pPlayer) const;
            bool CheckNormalAttackDelay(ATF::CPlayer* pPlayer) const;
            bool CheckForceAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub) const;
            bool CheckSkillAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, int indx) const;

            void SetUnitAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay);
            void SetSiegeAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay);
            void SetNormalAttackDelay(ATF::CPlayer* pPlayer, std::chrono::milliseconds msDelay);
            void SetForceAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, std::chrono::milliseconds msDelay);
            void SetSkillAttackDelay(ATF::CPlayer* pPlayer, int nCode, int nSub, int indx, std::chrono::milliseconds msDelay);

            // PvP tracking
            bool AlreadyKilled(ATF::CPlayer* pPlayer, uint32_t dwKillerSerial);
            bool PushSerialKiller(ATF::CPlayer* pPlayer, uint32_t dwKillerSerial);
            void CleanSerialKillerList(ATF::CPlayer* pPlayer);

            // Movement validation
            bool CheckMove(ATF::CPlayer* pPlayer, float* pfTarget) const;

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnableSetItemFix{ true };
            bool m_bEnableAttackDelayFix{ true };
            bool m_bEnablePvpTracking{ true };
            uint32_t m_setItemUpdateInterval{ 10000 }; // 10 seconds
            uint32_t m_pvpCleanupInterval{ 300000 };   // 5 minutes
            bool m_bLogPlayerActions{ false };

            // Player data storage
            std::unordered_map<uint32_t, detail::ContainerSetItemInfo_t> m_playerSetItems;
            std::unordered_map<uint32_t, detail::AttackDelay> m_playerAttackDelays;
            std::unordered_map<uint32_t, detail::PvpKillerInfo> m_playerPvpInfo;
            std::unordered_map<uint32_t, std::chrono::steady_clock::time_point> m_playerSetItemUpdate;

            mutable std::mutex m_playerDataMutex;

        private:
            // Helper functions
            std::chrono::milliseconds AdjustDelayValue(std::chrono::milliseconds msDelay) const;
            bool IsDelayExpired(const std::chrono::steady_clock::time_point& delayTime) const;
            void SetDelayTime(std::chrono::steady_clock::time_point& delayTime, std::chrono::milliseconds msDelay);

            // Set item helpers
            void DetectActiveSetEffects(ATF::CPlayer* pPlayer, detail::ContainerSetItemInfo_t& setInfo);
            bool ValidateSetItemEquipment(ATF::CPlayer* pPlayer, uint32_t dwSetIndex) const;

        private:
            static CPlayerEx* s_instance;
        };
    }
}
