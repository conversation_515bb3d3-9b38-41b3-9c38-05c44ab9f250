#include "pch.h"
#include "PlayerMoveSystem.h"
#include "PlayerEx.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"
#include <cmath>

namespace NexusPro
{
    namespace PlayerExtensions
    {
        CPlayerMoveSystem* CPlayerMoveSystem::s_instance = nullptr;

        bool CPlayerMoveSystem::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableMoveValidation = configManager.GetModuleConfigBool("playerext.move_system", "Validation", "enable_move_validation", true);
            m_bEnableSpeedCheck = configManager.GetModuleConfigBool("playerext.move_system", "Validation", "enable_speed_check", true);
            m_bEnablePositionCheck = configManager.GetModuleConfigBool("playerext.move_system", "Validation", "enable_position_check", true);
            m_maxMoveDistance = configManager.GetModuleConfigFloat("playerext.move_system", "Limits", "max_move_distance", 1000.0f);
            m_maxMoveSpeed = configManager.GetModuleConfigFloat("playerext.move_system", "Limits", "max_move_speed", 100.0f);
            m_bLogMovement = configManager.GetModuleConfigBool("playerext.move_system", "Logging", "log_movement", false);

            // Hook movement functions
            enable_hook(&ATF::CPlayer::pc_MoveNext, &CPlayerMoveSystem::pc_MoveNext);
            enable_hook(&ATF::CPlayer::pc_RealMovPos, &CPlayerMoveSystem::pc_RealMovPos);
            enable_hook(&ATF::CPlayer::pc_MoveStop, &CPlayerMoveSystem::pc_MoveStop);

            m_bInitialized = true;

            LOG_INFO("PlayerMoveSystem", "Player movement system initialized - Move Validation: " + std::to_string(m_bEnableMoveValidation) +
                     ", Speed Check: " + std::to_string(m_bEnableSpeedCheck) +
                     ", Position Check: " + std::to_string(m_bEnablePositionCheck) +
                     ", Max Distance: " + std::to_string(m_maxMoveDistance) +
                     ", Max Speed: " + std::to_string(m_maxMoveSpeed));

            return true;
        }

        void CPlayerMoveSystem::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();

            std::lock_guard<std::mutex> lock(m_movementDataMutex);
            m_playerMovementData.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("PlayerMoveSystem", "Player movement system shutdown");
        }

        void CPlayerMoveSystem::Configure(bool enableMoveValidation, bool enableSpeedCheck, bool enablePositionCheck,
                                         float maxMoveDistance, float maxMoveSpeed, bool logMovement)
        {
            m_bEnableMoveValidation = enableMoveValidation;
            m_bEnableSpeedCheck = enableSpeedCheck;
            m_bEnablePositionCheck = enablePositionCheck;
            m_maxMoveDistance = maxMoveDistance;
            m_maxMoveSpeed = maxMoveSpeed;
            m_bLogMovement = logMovement;
        }

        void CPlayerMoveSystem::InitializePlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_movementDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            PlayerMovementData& data = m_playerMovementData[playerSerial];
            data.lastPosition[0] = pPlayer->m_fCurPos[0];
            data.lastPosition[1] = pPlayer->m_fCurPos[1];
            data.lastPosition[2] = pPlayer->m_fCurPos[2];
            data.lastMoveTime = std::chrono::steady_clock::now();
            data.totalDistance = 0.0f;
            data.moveCount = 0;
            data.isValidating = true;

            if (m_bLogMovement)
            {
                LOG_DEBUG("PlayerMoveSystem", "Player movement tracking initialized: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void CPlayerMoveSystem::CleanupPlayer(ATF::CPlayer* pPlayer)
        {
            if (!pPlayer || !m_bInitialized)
                return;

            std::lock_guard<std::mutex> lock(m_movementDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;

            m_playerMovementData.erase(playerSerial);

            if (m_bLogMovement)
            {
                LOG_DEBUG("PlayerMoveSystem", "Player movement tracking cleaned up: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        bool CPlayerMoveSystem::ValidateMovement(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget)
        {
            if (!pPlayer || !pfCurrent || !m_bEnableMoveValidation)
                return true;

            // Check position validity
            if (m_bEnablePositionCheck && !CheckPositionValidity(pPlayer, pfCurrent))
            {
                if (m_bLogMovement)
                {
                    LOG_WARNING("PlayerMoveSystem", "Invalid position detected for player: " + std::string(pPlayer->m_wszPlayerName));
                }
                return false;
            }

            // Check movement distance and speed
            if (pfTarget)
            {
                if (!CheckMoveDistance(pPlayer, pfCurrent, pfTarget))
                {
                    if (m_bLogMovement)
                    {
                        LOG_WARNING("PlayerMoveSystem", "Invalid move distance for player: " + std::string(pPlayer->m_wszPlayerName));
                    }
                    return false;
                }

                if (m_bEnableSpeedCheck && !CheckMoveSpeed(pPlayer, pfCurrent, pfTarget))
                {
                    if (m_bLogMovement)
                    {
                        LOG_WARNING("PlayerMoveSystem", "Invalid move speed for player: " + std::string(pPlayer->m_wszPlayerName));
                    }
                    return false;
                }
            }

            // Update movement data
            UpdatePlayerMovementData(pPlayer, pfCurrent);

            return true;
        }

        bool CPlayerMoveSystem::CheckMoveSpeed(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget)
        {
            if (!pPlayer || !pfCurrent || !pfTarget)
                return true;

            std::lock_guard<std::mutex> lock(m_movementDataMutex);
            auto it = m_playerMovementData.find(pPlayer->m_dwSerial);
            if (it == m_playerMovementData.end())
                return true;

            auto now = std::chrono::steady_clock::now();
            auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - it->second.lastMoveTime);

            if (timeDiff.count() <= 0)
                return true;

            float distance = CalculateDistance(pfCurrent, pfTarget);
            float speed = CalculateSpeed(distance, timeDiff);

            return speed <= m_maxMoveSpeed;
        }

        bool CPlayerMoveSystem::CheckMoveDistance(ATF::CPlayer* pPlayer, float* pfCurrent, float* pfTarget)
        {
            if (!pPlayer || !pfCurrent || !pfTarget)
                return true;

            float distance = CalculateDistance(pfCurrent, pfTarget);
            return distance <= m_maxMoveDistance;
        }

        bool CPlayerMoveSystem::CheckPositionValidity(ATF::CPlayer* pPlayer, float* pfPosition)
        {
            if (!pPlayer || !pfPosition)
                return false;

            // Basic position validation - check for reasonable coordinates
            for (int i = 0; i < 3; ++i)
            {
                if (std::isnan(pfPosition[i]) || std::isinf(pfPosition[i]))
                    return false;

                // Check for extremely large coordinates (likely invalid)
                if (std::abs(pfPosition[i]) > 100000.0f)
                    return false;
            }

            return true;
        }

        float CPlayerMoveSystem::CalculateDistance(float* pos1, float* pos2) const
        {
            if (!pos1 || !pos2)
                return 0.0f;

            float dx = pos2[0] - pos1[0];
            float dy = pos2[1] - pos1[1];
            float dz = pos2[2] - pos1[2];

            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }

        float CPlayerMoveSystem::CalculateSpeed(float distance, std::chrono::milliseconds timeDiff) const
        {
            if (timeDiff.count() <= 0)
                return 0.0f;

            // Speed in units per second
            return distance / (timeDiff.count() / 1000.0f);
        }

        void CPlayerMoveSystem::UpdatePlayerMovementData(ATF::CPlayer* pPlayer, float* pfPosition)
        {
            if (!pPlayer || !pfPosition)
                return;

            std::lock_guard<std::mutex> lock(m_movementDataMutex);
            auto& data = m_playerMovementData[pPlayer->m_dwSerial];

            float distance = CalculateDistance(data.lastPosition, pfPosition);
            data.totalDistance += distance;
            data.moveCount++;

            // Update last position and time
            data.lastPosition[0] = pfPosition[0];
            data.lastPosition[1] = pfPosition[1];
            data.lastPosition[2] = pfPosition[2];
            data.lastMoveTime = std::chrono::steady_clock::now();
        }

        void WINAPIV CPlayerMoveSystem::pc_MoveNext(
            ATF::CPlayer* pPlayer,
            char byMoveType,
            float* pfCur,
            float* pfTar,
            char byDirect,
            ATF::Info::CPlayerpc_MoveNext1793_ptr next)
        {
            if (!pPlayer || !pfCur || !pfTar || !s_instance)
            {
                next(pPlayer, byMoveType, pfCur, pfTar, byDirect);
                return;
            }

            // Validate movement
            if (s_instance->ValidateMovement(pPlayer, pfCur, pfTar))
            {
                next(pPlayer, byMoveType, pfCur, pfTar, byDirect);
            }
            else if (s_instance->m_bLogMovement)
            {
                LOG_WARNING("PlayerMoveSystem", "MoveNext blocked for player: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void WINAPIV CPlayerMoveSystem::pc_RealMovPos(
            ATF::CPlayer* pPlayer,
            float* pfCur,
            ATF::Info::CPlayerpc_RealMovPos1879_ptr next)
        {
            if (!pPlayer || !pfCur || !s_instance)
            {
                next(pPlayer, pfCur);
                return;
            }

            // Validate movement
            if (s_instance->ValidateMovement(pPlayer, pfCur))
            {
                next(pPlayer, pfCur);
            }
            else if (s_instance->m_bLogMovement)
            {
                LOG_WARNING("PlayerMoveSystem", "RealMovPos blocked for player: " + std::string(pPlayer->m_wszPlayerName));
            }
        }

        void WINAPIV CPlayerMoveSystem::pc_MoveStop(
            ATF::CPlayer* pPlayer,
            float* pfCur,
            ATF::Info::CPlayerpc_MoveStop1797_ptr next)
        {
            if (!pPlayer || !pfCur || !s_instance)
            {
                next(pPlayer, pfCur);
                return;
            }

            // Validate movement
            if (s_instance->ValidateMovement(pPlayer, pfCur))
            {
                next(pPlayer, pfCur);
            }
            else if (s_instance->m_bLogMovement)
            {
                LOG_WARNING("PlayerMoveSystem", "MoveStop blocked for player: " + std::string(pPlayer->m_wszPlayerName));
            }
        }
    }
}
