<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Core">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common\Helpers">
      <UniqueIdentifier>{D9D6E242-F8AF-46E4-B9FD-80ECBC20BA3E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common\Interfaces">
      <UniqueIdentifier>{E12C6187-8E87-4B6B-9FAE-652D4B3F7CDB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules">
      <UniqueIdentifier>{F2A71F9B-5D33-465A-A702-920D77279786}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\CoreFixes">
      <UniqueIdentifier>{9FC0E8F0-5A51-4FDB-96C8-4C4920C30E95}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\Addons">
      <UniqueIdentifier>{2DAB880C-F5F2-4E4F-9BA2-8E2B3E6F8A4D}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="framework.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Core\NexusCore.h">
      <Filter>Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\ModuleManager.h">
      <Filter>Core</Filter>
    </ClInclude>
    <ClInclude Include="Core\ConfigManager.h">
      <Filter>Core</Filter>
    </ClInclude>
    <ClInclude Include="Common\Helpers\SingletonHelper.h">
      <Filter>Common\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="Common\Helpers\ModuleHook.h">
      <Filter>Common\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="Common\Helpers\Memory.h">
      <Filter>Common\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="Common\Interfaces\ModuleInterface.h">
      <Filter>Common\Interfaces</Filter>
    </ClInclude>
    <ClInclude Include="Common\ETypes.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\Player.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\AttackSystem.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\NetworkEx.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\CheatCommand.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\CrashDump.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\MainThread.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\PotionMgr.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\Guild.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\Vote.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\CoreFixes\Store.h">
      <Filter>Modules\CoreFixes</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\AccuracyEffect.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\ServerMemoryPatch.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\ChatLog.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\EnchantChance.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\LootExchange.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\BonusStart.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\DefenceFormula.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\GMCommands.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\StoneHP.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\PvpPotion.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\MauExp.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\Advert.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\RadiusDropLoot.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\ReplaceLootName.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
    <ClInclude Include="Modules\Addons\VariousSettings.h">
      <Filter>Modules\Addons</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Core\NexusCore.cpp">
      <Filter>Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\ModuleManager.cpp">
      <Filter>Core</Filter>
    </ClCompile>
    <ClCompile Include="Core\ConfigManager.cpp">
      <Filter>Core</Filter>
    </ClCompile>
    <ClCompile Include="Common\Helpers\Memory.cpp">
      <Filter>Common\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\Player.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\AttackSystem.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\NetworkEx.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\CheatCommand.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\CrashDump.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\MainThread.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\PotionMgr.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\Guild.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\Vote.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\CoreFixes\Store.cpp">
      <Filter>Modules\CoreFixes</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\AccuracyEffect.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\ServerMemoryPatch.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\ChatLog.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\EnchantChance.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\LootExchange.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\BonusStart.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\DefenceFormula.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\GMCommands.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\StoneHP.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\PvpPotion.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\MauExp.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\Advert.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\RadiusDropLoot.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\ReplaceLootName.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
    <ClCompile Include="Modules\Addons\VariousSettings.cpp">
      <Filter>Modules\Addons</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
