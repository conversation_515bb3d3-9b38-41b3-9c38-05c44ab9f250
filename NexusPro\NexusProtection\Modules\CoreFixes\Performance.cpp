#include "pch.h"
#include "Performance.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"
#include <cmath>
#include <immintrin.h>

namespace NexusPro
{
    namespace CoreFixes
    {
        CPerformance* CPerformance::s_instance = nullptr;

        bool CPerformance::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnableOptimizations = configManager.GetModuleConfigBool("corefix.performance", "Optimizations", "enable_optimizations", true);
            m_bEnableMetrics = configManager.GetModuleConfigBool("corefix.performance", "Monitoring", "enable_metrics", false);
            m_bEnableSIMD = configManager.GetModuleConfigBool("corefix.performance", "Optimizations", "enable_simd", true);
            m_bLogPerformance = configManager.GetModuleConfigBool("corefix.performance", "Logging", "log_performance", false);
            m_metricsInterval = configManager.GetModuleConfigInt("corefix.performance", "Monitoring", "metrics_interval", 60000);

            // Hook performance-critical functions
            if (m_bEnableOptimizations)
            {
                enable_hook(&ATF::Global::GetSqrt, &CPerformance::GetSqrt);
                enable_hook(&ATF::Global::Get3DSqrt, &CPerformance::Get3DSqrt);
                enable_hook((float(*)(float*, float*))&ATF::Global::GetYAngle, &CPerformance::GetYAngle);
                enable_hook(&ATF::Global::GetDist, &CPerformance::GetDist);
            }

            m_lastReport = std::chrono::steady_clock::now();
            m_bInitialized = true;

            LOG_INFO("Performance", "Performance optimization initialized - Optimizations: " + std::to_string(m_bEnableOptimizations) +
                     ", Metrics: " + std::to_string(m_bEnableMetrics) +
                     ", SIMD: " + std::to_string(m_bEnableSIMD));

            return true;
        }

        void CPerformance::Shutdown()
        {
            if (!m_bInitialized)
                return;

            if (m_bLogPerformance)
            {
                LogPerformanceReport();
            }

            cleanup_all_hook();

            std::lock_guard<std::mutex> lock(m_metricsMutex);
            m_performanceMetrics.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("Performance", "Performance optimization shutdown");
        }

        void CPerformance::Configure(bool enableOptimizations, bool enableMetrics, bool enableSIMD,
                                    bool logPerformance, uint32_t metricsInterval)
        {
            m_bEnableOptimizations = enableOptimizations;
            m_bEnableMetrics = enableMetrics;
            m_bEnableSIMD = enableSIMD;
            m_bLogPerformance = logPerformance;
            m_metricsInterval = metricsInterval;
        }

        void CPerformance::UpdateMetrics(const std::string& functionName, uint64_t executionTime)
        {
            if (!m_bEnableMetrics)
                return;

            std::lock_guard<std::mutex> lock(m_metricsMutex);
            auto& metrics = m_performanceMetrics[functionName];

            metrics.totalCalls++;
            metrics.totalTime += executionTime;
            metrics.minTime = std::min(metrics.minTime, executionTime);
            metrics.maxTime = std::max(metrics.maxTime, executionTime);
            metrics.avgTime = static_cast<double>(metrics.totalTime) / metrics.totalCalls;
        }

        PerformanceMetrics CPerformance::GetMetrics(const std::string& functionName) const
        {
            std::lock_guard<std::mutex> lock(m_metricsMutex);
            auto it = m_performanceMetrics.find(functionName);
            if (it != m_performanceMetrics.end())
                return it->second;
            return PerformanceMetrics{};
        }

        void CPerformance::ResetMetrics()
        {
            std::lock_guard<std::mutex> lock(m_metricsMutex);
            m_performanceMetrics.clear();
        }

        void CPerformance::LogPerformanceReport()
        {
            if (!m_bLogPerformance)
                return;

            std::lock_guard<std::mutex> lock(m_metricsMutex);
            
            LOG_INFO("Performance", "=== Performance Report ===");
            for (const auto& [name, metrics] : m_performanceMetrics)
            {
                LOG_INFO("Performance", name + " - Calls: " + std::to_string(metrics.totalCalls) +
                        ", Avg: " + std::to_string(metrics.avgTime) + "μs" +
                        ", Min: " + std::to_string(metrics.minTime) + "μs" +
                        ", Max: " + std::to_string(metrics.maxTime) + "μs");
            }
        }

        float CPerformance::FastSqrt2D(float* fPos, float* fTar)
        {
            if (!fPos || !fTar)
                return 0.0f;

            float dx = fTar[0] - fPos[0];
            float dz = fTar[2] - fPos[2];
            return std::sqrt(dx * dx + dz * dz);
        }

        float CPerformance::FastSqrt3D(float* fPos, float* fTar)
        {
            if (!fPos || !fTar)
                return 0.0f;

            float dx = fTar[0] - fPos[0];
            float dy = fTar[1] - fPos[1];
            float dz = fTar[2] - fPos[2];
            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }

        float CPerformance::FastDistance(const float* fPos, const float* fTar)
        {
            if (!fPos || !fTar)
                return 0.0f;

            float dx = fTar[0] - fPos[0];
            float dz = fTar[2] - fPos[2];
            return std::sqrt(dx * dx + dz * dz);
        }

        float CPerformance::FastYAngle(float* fPos, float* fTar)
        {
            if (!fPos || !fTar)
                return 0.0f;

            float dx = fTar[0] - fPos[0];
            float dz = fTar[2] - fPos[2];
            
            float distance = std::sqrt(dx * dx + dz * dz);
            if (distance == 0.0f)
                return 0.0f;

            double acos_val = std::acos(dz / distance);
            
            if (dx > 0.0f)
                return static_cast<float>(PiToAngle(acos_val) + 32768);
            else
                return static_cast<float>(PiToAngle(acos_val));
        }

        float WINAPIV CPerformance::GetSqrt(
            float* fPos,
            float* fTar,
            ATF::Global::Info::GetSqrt667_ptr next)
        {
            if (!s_instance || !s_instance->m_bEnableOptimizations)
                return next(fPos, fTar);

            if (s_instance->m_bEnableMetrics)
            {
                PERF_TIMER("GetSqrt");
                return FastSqrt2D(fPos, fTar);
            }

            return FastSqrt2D(fPos, fTar);
        }

        float WINAPIV CPerformance::Get3DSqrt(
            float* fPos,
            float* fTar,
            ATF::Global::Info::Get3DSqrt435_ptr next)
        {
            if (!s_instance || !s_instance->m_bEnableOptimizations)
                return next(fPos, fTar);

            if (s_instance->m_bEnableMetrics)
            {
                PERF_TIMER("Get3DSqrt");
                return FastSqrt3D(fPos, fTar);
            }

            return FastSqrt3D(fPos, fTar);
        }

        float WINAPIV CPerformance::GetYAngle(
            float* fPos,
            float* fTar,
            ATF::Global::Info::GetYAngle700_ptr next)
        {
            if (!s_instance || !s_instance->m_bEnableOptimizations)
                return next(fPos, fTar);

            if (s_instance->m_bEnableMetrics)
            {
                PERF_TIMER("GetYAngle");
                return FastYAngle(fPos, fTar);
            }

            return FastYAngle(fPos, fTar);
        }

        float WINAPIV CPerformance::GetDist(
            const float* fPos,
            const float* fTar,
            ATF::Global::Info::GetDist498_ptr next)
        {
            if (!s_instance || !s_instance->m_bEnableOptimizations)
                return next(fPos, fTar);

            if (s_instance->m_bEnableMetrics)
            {
                PERF_TIMER("GetDist");
                return FastDistance(fPos, fTar);
            }

            return FastDistance(fPos, fTar);
        }

        // Performance Timer Implementation
        CPerformance::PerformanceTimer::PerformanceTimer(const std::string& name)
            : m_functionName(name)
            , m_startTime(std::chrono::high_resolution_clock::now())
        {
        }

        CPerformance::PerformanceTimer::~PerformanceTimer()
        {
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - m_startTime);
            
            if (s_instance)
            {
                s_instance->UpdateMetrics(m_functionName, duration.count());
            }
        }
    }
}
