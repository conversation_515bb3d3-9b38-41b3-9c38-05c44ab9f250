[General]
; Store System Fix Module
; This module fixes various store/shop system issues and exploits
enabled=true

[Fixes]
; Enable item overlap validation (prevent selling non-stackable items with quantity > 1)
enable_overlap_fix=true

; Enable price calculation fixes
enable_price_fix=true

; Enable discount rate validation and fixes
enable_discount_fix=true

; Enable price overflow protection
enable_overflow_fix=true

[Limits]
; Maximum allowed discount rate (0.0 to 1.0)
max_discount_rate=1.0

; Maximum price per transaction (prevents overflow)
max_transaction_price=4294967295

; Maximum items per transaction
max_items_per_transaction=100

[Security]
; Enable transaction validation
enable_transaction_validation=true

; Prevent negative prices
prevent_negative_prices=true

; Enable race-based price validation
enable_race_price_validation=true

[Economy]
; Enable dynamic pricing based on server economy
enable_dynamic_pricing=false

; Price adjustment factor for different races
race_price_multiplier=1.0

; Enable store tax system
enable_store_tax=false

; Store tax rate (percentage)
store_tax_rate=0.05

[Logging]
; Enable detailed transaction logging
log_transactions=false

; Log blocked transactions
log_blocked_transactions=true

; Log price calculations
log_price_calculations=false

; Log discount applications
log_discount_applications=false

[Description]
; Store module provides:
; - Item overlap validation to prevent non-stackable item exploits
; - Price calculation fixes and overflow protection
; - Discount rate validation and limits
; - Race-based price multiplier validation
; - Transaction security and validation
; - Comprehensive store transaction logging
; - Protection against store-related exploits and dupes
; - Support for multiple currency types (Dalant, Gold, Points)
