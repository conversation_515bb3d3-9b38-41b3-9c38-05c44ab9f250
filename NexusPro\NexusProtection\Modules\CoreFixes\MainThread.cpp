#include "pch.h"
#include "MainThread.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CMainThread* CMainThread::s_instance = nullptr;

        bool CMainThread::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_priority1 = configManager.GetModuleConfigInt("corefix.main_thread", "Performance", "priority_1", 100);
            m_priority2 = configManager.GetModuleConfigInt("corefix.main_thread", "Performance", "priority_2", 500);
            m_priority3 = configManager.GetModuleConfigInt("corefix.main_thread", "Performance", "priority_3", 1000);
            m_priority4 = configManager.GetModuleConfigInt("corefix.main_thread", "Performance", "priority_4", 2000);
            m_priority5 = configManager.GetModuleConfigInt("corefix.main_thread", "Performance", "priority_5", 5000);

            // Setup priority timers
            detail::PriorityTimer_t priorityTimerValue{
                { detail::priority_task::one,   std::chrono::milliseconds(m_priority1) },
                { detail::priority_task::two,   std::chrono::milliseconds(m_priority2) },
                { detail::priority_task::three, std::chrono::milliseconds(m_priority3) },
                { detail::priority_task::four,  std::chrono::milliseconds(m_priority4) },
                { detail::priority_task::five,  std::chrono::milliseconds(m_priority5) }
            };

            // Create tasks
            MakeTasks(priorityTimerValue);

            // Hook main thread functions
            enable_hook(&ATF::CMainThread::CheckForceClose, &CMainThread::CheckForceClose);
            enable_hook(&ATF::CMainThread::OnRun, &CMainThread::OnRun);

            m_bInitialized = true;

            LOG_INFO("MainThread", "Main thread optimization initialized with priorities: " +
                     std::to_string(m_priority1) + "ms, " + std::to_string(m_priority2) + "ms, " +
                     std::to_string(m_priority3) + "ms, " + std::to_string(m_priority4) + "ms, " +
                     std::to_string(m_priority5) + "ms");

            return true;
        }

        void CMainThread::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();
            m_tasks.clear();
            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("MainThread", "Main thread optimization shutdown");
        }

        void CMainThread::Configure(uint32_t priority1, uint32_t priority2, uint32_t priority3,
                                   uint32_t priority4, uint32_t priority5)
        {
            m_priority1 = priority1;
            m_priority2 = priority2;
            m_priority3 = priority3;
            m_priority4 = priority4;
            m_priority5 = priority5;
        }

        void CMainThread::MakeTasks(const detail::PriorityTimer_t& priorityTimerValue)
        {
            m_tasks.clear();

            // Priority 1 tasks (highest frequency) - Critical systems
            m_tasks.emplace_back(
                detail::CRunRule(priorityTimerValue.at(detail::priority_task::one)),
                []() {
                    // High priority tasks - player updates, network processing
                    // This would be implemented based on specific server needs
                }
            );

            // Priority 2 tasks - Important systems
            m_tasks.emplace_back(
                detail::CRunRule(priorityTimerValue.at(detail::priority_task::two)),
                []() {
                    // Medium-high priority tasks - combat systems, movement
                }
            );

            // Priority 3 tasks - Regular systems
            m_tasks.emplace_back(
                detail::CRunRule(priorityTimerValue.at(detail::priority_task::three)),
                []() {
                    // Medium priority tasks - guild updates, chat processing
                }
            );

            // Priority 4 tasks - Background systems
            m_tasks.emplace_back(
                detail::CRunRule(priorityTimerValue.at(detail::priority_task::four)),
                []() {
                    // Low priority tasks - statistics, logging
                }
            );

            // Priority 5 tasks (lowest frequency) - Maintenance
            m_tasks.emplace_back(
                detail::CRunRule(priorityTimerValue.at(detail::priority_task::five)),
                []() {
                    // Very low priority tasks - cleanup, maintenance
                }
            );
        }

        void WINAPIV CMainThread::CheckForceClose(
            ATF::CMainThread* pObj,
            ATF::Info::CMainThreadCheckForceClose24_ptr next)
        {
            if (!pObj)
                return;

            // Call original function
            next(pObj);

            // Additional force close checks can be added here
            if (s_instance)
            {
                // Custom force close logic if needed
            }
        }

        void WINAPIV CMainThread::OnRun(
            ATF::CMainThread* pObj,
            ATF::Info::CMainThreadOnRun130_ptr next)
        {
            if (!pObj)
                return;

            // Execute our priority tasks before the original run
            if (s_instance)
            {
                for (const auto& task : s_instance->m_tasks)
                {
                    task.run();
                }
            }

            // Call original OnRun function
            next(pObj);
        }
    }
}
