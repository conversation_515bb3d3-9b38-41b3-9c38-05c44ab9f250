#include "pch.h"
#include "Vote.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CVote* CVote::s_instance = nullptr;

        bool CVote::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_level = configManager.GetModuleConfigInt("corefix.vote", "Requirements", "level", 30);
            m_playTime = configManager.GetModuleConfigInt("corefix.vote", "Requirements", "play_time", UINT32_MAX);
            m_pvpPoint = configManager.GetModuleConfigDouble("corefix.vote", "Requirements", "pvp_point", -1.0);
            m_pvpCashBag = configManager.GetModuleConfigDouble("corefix.vote", "Requirements", "pvp_cash_bag", -1.0);
            m_classGrade = configManager.GetModuleConfigInt("corefix.vote", "Requirements", "class_grade", -1);
            m_bScoreListShow = configManager.GetModuleConfigBool("corefix.vote", "Display", "score_list_show", true);
            m_bScoreHide = configManager.GetModuleConfigBool("corefix.vote", "Display", "score_hide", false);
            m_bLogVoteActions = configManager.GetModuleConfigBool("corefix.vote", "Logging", "log_vote_actions", false);

            // Hook vote functions
            enable_hook(&ATF::Voter::_Vote, &CVote::Vote);
            enable_hook(&ATF::Voter::_SendVotePaper, &CVote::SendVotePaper);
            enable_hook(&ATF::Voter::_SendVotePaperAll, &CVote::SendVotePaperAll);
            enable_hook(&ATF::Voter::_SendVoteScore, &CVote::SendVoteScore);
            enable_hook(&ATF::Voter::_SendVoteScoreAll, &CVote::SendVoteScoreAll);

            m_bInitialized = true;

            LOG_INFO("Vote", "Vote system fixes initialized - Level: " + std::to_string(m_level) +
                     ", Play Time: " + std::to_string(m_playTime) +
                     ", PvP Point: " + std::to_string(m_pvpPoint) +
                     ", PvP Cash Bag: " + std::to_string(m_pvpCashBag) +
                     ", Class Grade: " + std::to_string(m_classGrade));

            return true;
        }

        void CVote::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();
            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("Vote", "Vote system fixes shutdown");
        }

        void CVote::Configure(int32_t level, uint32_t playTime, double pvpPoint, double pvpCashBag,
                             int32_t classGrade, bool scoreListShow, bool scoreHide)
        {
            m_level = level;
            m_playTime = playTime;
            m_pvpPoint = pvpPoint;
            m_pvpCashBag = pvpCashBag;
            m_classGrade = classGrade;
            m_bScoreListShow = scoreListShow;
            m_bScoreHide = scoreHide;
        }

        bool CVote::CheckConditions(ATF::CPlayer* pPlayer, bool bView)
        {
            if (!pPlayer || !pPlayer->m_bOper)
                return false;

            // Check if voting is enabled for this player (unless just viewing)
            if (!bView && !pPlayer->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable)
                return false;

            // Check all voting requirements
            if (!CheckClassGrade(pPlayer->m_pUserDB->m_AvatorData.dbAvator.m_byLastClassGrade))
                return false;

            if (!CheckLevel(pPlayer->GetLevel()))
                return false;

            if (!CheckPvpCashBag(pPlayer->GetPvpOrderView()->GetPvpCash()))
                return false;

            if (!CheckPvpPoint(pPlayer->m_Param.GetPvPPoint()))
                return false;

            if (!CheckPlayTime(pPlayer->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime))
                return false;

            return true;
        }

        bool CVote::CheckLevel(int32_t level) const
        {
            return level >= m_level;
        }

        bool CVote::CheckClassGrade(int32_t classGrade) const
        {
            if (m_classGrade == -1) // No class grade requirement
                return true;
            return classGrade >= m_classGrade;
        }

        bool CVote::CheckPlayTime(uint32_t playTime) const
        {
            if (m_playTime == UINT32_MAX) // No play time requirement
                return true;
            return playTime >= m_playTime;
        }

        bool CVote::CheckPvpPoint(double pvpPoint) const
        {
            if (m_pvpPoint < 0.0) // No PvP point requirement
                return true;
            return pvpPoint >= m_pvpPoint;
        }

        bool CVote::CheckPvpCashBag(double pvpCashBag) const
        {
            if (m_pvpCashBag < 0.0) // No PvP cash bag requirement
                return true;
            return pvpCashBag >= m_pvpCashBag;
        }

        int WINAPIV CVote::SendVotePaper(
            ATF::Voter* pObj,
            ATF::CPlayer* pPlayer,
            ATF::Info::Voter_SendVotePaper12_ptr next)
        {
            if (!pObj || !pPlayer || !s_instance)
                return next(pObj, pPlayer);

            // Check if player meets voting conditions
            if (!s_instance->CheckConditions(pPlayer, false))
            {
                if (s_instance->m_bLogVoteActions)
                {
                    LOG_DEBUG("Vote", "Vote paper blocked - Player does not meet requirements: " + 
                             std::string(pPlayer->m_wszPlayerName));
                }
                return 1; // Block vote paper
            }

            if (s_instance->m_bLogVoteActions)
            {
                LOG_DEBUG("Vote", "Vote paper sent to player: " + std::string(pPlayer->m_wszPlayerName));
            }

            return next(pObj, pPlayer);
        }

        void WINAPIV CVote::SendVotePaperAll(
            ATF::Voter* pObj,
            ATF::Info::Voter_SendVotePaperAll14_ptr next)
        {
            if (!pObj || !s_instance)
            {
                next(pObj);
                return;
            }

            if (s_instance->m_bLogVoteActions)
            {
                LOG_DEBUG("Vote", "Sending vote papers to all eligible players");
            }

            next(pObj);
        }

        void WINAPIV CVote::SendVoteScore(
            ATF::Voter* pObj,
            ATF::CPlayer* pPlayer,
            ATF::Info::Voter_SendVoteScore16_ptr next)
        {
            if (!pObj || !pPlayer || !s_instance)
            {
                next(pObj, pPlayer);
                return;
            }

            // Check if score should be hidden or if player can view scores
            if (s_instance->m_bScoreHide && !s_instance->CheckConditions(pPlayer, true))
            {
                if (s_instance->m_bLogVoteActions)
                {
                    LOG_DEBUG("Vote", "Vote score hidden from player: " + std::string(pPlayer->m_wszPlayerName));
                }
                return; // Don't send score
            }

            if (s_instance->m_bLogVoteActions)
            {
                LOG_DEBUG("Vote", "Vote score sent to player: " + std::string(pPlayer->m_wszPlayerName));
            }

            next(pObj, pPlayer);
        }

        void WINAPIV CVote::SendVoteScoreAll(
            ATF::Voter* pObj,
            char byRace,
            ATF::Info::Voter_SendVoteScoreAll18_ptr next)
        {
            if (!pObj || !s_instance)
            {
                next(pObj, byRace);
                return;
            }

            // Check if score list should be shown
            if (!s_instance->m_bScoreListShow)
            {
                if (s_instance->m_bLogVoteActions)
                {
                    LOG_DEBUG("Vote", "Vote score list broadcast blocked for race: " + std::to_string(byRace));
                }
                return; // Don't broadcast scores
            }

            if (s_instance->m_bLogVoteActions)
            {
                LOG_DEBUG("Vote", "Vote score list broadcast to race: " + std::to_string(byRace));
            }

            next(pObj, byRace);
        }

        int WINAPIV CVote::Vote(
            ATF::Voter* pObj,
            ATF::CPlayer* pPlayer,
            char* pData,
            ATF::Info::Voter_Vote22_ptr next)
        {
            if (!pObj || !pPlayer || !pData || !s_instance)
                return next(pObj, pPlayer, pData);

            // Check if player meets voting conditions
            if (!s_instance->CheckConditions(pPlayer, false))
            {
                if (s_instance->m_bLogVoteActions)
                {
                    LOG_WARNING("Vote", "Vote attempt blocked - Player does not meet requirements: " + 
                               std::string(pPlayer->m_wszPlayerName));
                }
                return 1; // Block vote
            }

            if (s_instance->m_bLogVoteActions)
            {
                LOG_INFO("Vote", "Vote cast by player: " + std::string(pPlayer->m_wszPlayerName));
            }

            return next(pObj, pPlayer, pData);
        }
    }
}
