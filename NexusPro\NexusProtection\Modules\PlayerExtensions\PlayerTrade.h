#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CPlayerInfo.hpp"
#include <unordered_map>
#include <chrono>
#include <mutex>

namespace NexusPro
{
    namespace PlayerExtensions
    {
        struct TradeData
        {
            uint32_t partnerSerial{ 0 };
            std::chrono::steady_clock::time_point tradeStartTime;
            uint32_t tradeAttempts{ 0 };
            bool isValidating{ true };
        };

        class CPlayerTrade : public IModuleInterface, public SingletonHelper::CSingleton<CPlayerTrade>
        {
        public:
            CPlayerTrade() = default;
            virtual ~CPlayerTrade() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "PlayerTrade"; }

            // Configuration
            void Configure(bool enableTradeValidation, bool enableRaceCheck, bool enableDegradeCheck,
                          bool enableItemValidation, bool enableAntiDupe, bool logTrades);

            // Player management
            void InitializePlayer(ATF::CPlayer* pPlayer);
            void CleanupPlayer(ATF::CPlayer* pPlayer);

            // Trade validation
            bool ValidateTradeRequest(ATF::CPlayer* pPlayer, ATF::CPlayer* pPartner);
            bool ValidateTradeItems(ATF::CPlayer* pPlayer);
            bool ValidateItemExchangeability(ATF::CPlayer* pPlayer, uint32_t itemSerial, uint8_t storageCode);
            bool CheckTradeRestrictions(ATF::CPlayer* pPlayer, ATF::CPlayer* pPartner);

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnableTradeValidation{ true };
            bool m_bEnableRaceCheck{ true };
            bool m_bEnableDegradeCheck{ true };
            bool m_bEnableItemValidation{ true };
            bool m_bEnableAntiDupe{ true };
            bool m_bLogTrades{ false };

            // Trade tracking
            std::unordered_map<uint32_t, TradeData> m_playerTradeData;
            mutable std::mutex m_tradeDataMutex;

        private:
            // Hook functions
            static void WINAPIV pc_DTradeOKRequest(
                ATF::CPlayer* pObj,
                unsigned int* pdwKey,
                ATF::Info::CPlayerpc_DTradeOKRequest1687_ptr next);

            // Helper functions
            bool CheckRaceCompatibility(ATF::CPlayer* pPlayer1, ATF::CPlayer* pPlayer2) const;
            bool CheckDegradeCompatibility(ATF::CPlayer* pPlayer1, ATF::CPlayer* pPlayer2) const;
            bool CheckTradeMode(ATF::CPlayer* pPlayer) const;
            bool CheckTradeLock(ATF::CPlayer* pPlayer) const;
            bool ValidateExchangeItems(ATF::CPlayer* pPlayer) const;

        private:
            static CPlayerTrade* s_instance;
        };
    }
}
