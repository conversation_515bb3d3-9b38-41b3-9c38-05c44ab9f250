#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CPlayerInfo.hpp"
#include "../../Common/ATF/CPostSystemManagerInfo.hpp"
#include "../../Common/ATF/CMainThreadInfo.hpp"
#include <unordered_map>
#include <mutex>

namespace NexusPro
{
    namespace CoreFixes
    {
        struct PostSystemMetrics
        {
            uint32_t totalPostsSent{ 0 };
            uint32_t totalPostsReceived{ 0 };
            uint32_t totalPostsReturned{ 0 };
            uint32_t failedPosts{ 0 };
            uint64_t totalGoldTransferred{ 0 };
        };

        class CPostSystem : public IModuleInterface, public SingletonHelper::CSingleton<CPostSystem>
        {
        public:
            CPostSystem() = default;
            virtual ~CPostSystem() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "PostSystem"; }

            // Configuration
            void Configure(bool enablePostFixes, bool enableValidation, bool enableMetrics,
                          uint32_t maxPostsPerPlayer, uint64_t maxGoldPerPost, bool logPostActivity);

            // Post system management
            bool ValidatePostRegistration(ATF::CPlayer* pPlayer, ATF::_STORAGE_POS_INDIV* pItemInfo, 
                                        uint32_t dwGold, ATF::_STORAGE_LIST::_db_con** pItem);
            void UpdatePostMetrics(const std::string& operation, uint32_t count = 1, uint64_t gold = 0);

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnablePostFixes{ true };
            bool m_bEnableValidation{ true };
            bool m_bEnableMetrics{ false };
            uint32_t m_maxPostsPerPlayer{ 100 };
            uint64_t m_maxGoldPerPost{ 1000000 };
            bool m_bLogPostActivity{ false };

            // Post system tracking
            PostSystemMetrics m_metrics;
            std::unordered_map<uint32_t, uint32_t> m_playerPostCounts;
            mutable std::mutex m_postDataMutex;

        private:
            // Hook functions
            static void WINAPIV CMainThread_Load_ReturnPost_Complete(
                ATF::CMainThread* pObj,
                char* pData,
                ATF::Info::CMainThreadLoad_ReturnPost_Complete114_ptr next);

            static void WINAPIV CMainThread_Load_PostStorage_Complete(
                ATF::CMainThread* pObj,
                char* pData,
                ATF::Info::CMainThreadLoad_PostStorage_Complete112_ptr next);

            static char WINAPIV CheckRegister(
                ATF::CPostSystemManager* pObj,
                ATF::CPlayer* pOne,
                ATF::_STORAGE_POS_INDIV* pItemInfo,
                unsigned int dwGold,
                ATF::_STORAGE_LIST::_db_con** pItem,
                ATF::Info::CPostSystemManagerCheckRegister4_ptr next);

        private:
            // Helper functions
            bool ValidatePostData(ATF::CPlayer* pPlayer, uint32_t dwGold);
            bool ValidateItemData(ATF::_STORAGE_POS_INDIV* pItemInfo, ATF::_STORAGE_LIST::_db_con** pItem);
            void ProcessReturnPostList(ATF::CPlayer* pPlayer, ATF::_qry_case_post_return_list_get* pInfo);
            void ProcessPostStorageList(ATF::CPlayer* pPlayer, ATF::_qry_case_post_storage_list_get* pInfo);

        private:
            static CPostSystem* s_instance;
        };
    }
}
