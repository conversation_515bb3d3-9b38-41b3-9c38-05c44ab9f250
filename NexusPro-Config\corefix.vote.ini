[General]
; Vote System Fix Module
; This module fixes and enhances the voting system with configurable requirements
enabled=true

[Requirements]
; Minimum level required to vote
level=30

; Minimum play time required (in seconds, -1 = no requirement)
play_time=-1

; Minimum PvP points required (-1.0 = no requirement)
pvp_point=-1.0

; Minimum PvP cash bag required (-1.0 = no requirement)
pvp_cash_bag=-1.0

; Minimum class grade required (-1 = no requirement)
class_grade=0

[Display]
; Show vote score list to all players
score_list_show=true

; Hide vote scores from players who don't meet requirements
score_hide=false

[Restrictions]
; Enable vote eligibility checking
enable_eligibility_check=true

; Block voting during specific events
block_during_siege=false

; Block voting during guild battles
block_during_guild_battle=false

[Security]
; Prevent vote manipulation
prevent_vote_spam=true

; Maximum votes per player per election
max_votes_per_election=1

; Cooldown between votes (seconds)
vote_cooldown=300

[Logging]
; Enable detailed vote action logging
log_vote_actions=false

; Log blocked vote attempts
log_blocked_votes=true

; Log vote eligibility checks
log_eligibility_checks=false

[Description]
; Vote module provides:
; - Configurable voting requirements (level, play time, PvP stats, class grade)
; - Vote eligibility validation and enforcement
; - Score display control and restrictions
; - Vote spam prevention and security measures
; - Comprehensive vote action logging
; - Integration with patriarch election system
; - Support for race-specific voting restrictions
