[General]
; Potion Manager Fix Module
; This module fixes various potion-related issues and exploits
enabled=true

[Fixes]
; Enable potion stacking prevention
enable_stacking_fix=true

; Enable potion delay enforcement
enable_delay_fix=true

; Enable effect limitation checks
enable_effect_limit_fix=true

; Enable cash item potion restrictions
enable_cash_item_fix=true

; Enable PvP potion restrictions
enable_pvp_potion_fix=true

[Restrictions]
; Restricted cash item effect types (comma-separated)
restricted_cash_effects=48,49,70,71

; Minimum potion delay in milliseconds
min_potion_delay=1000

; Maximum stacked effects per player
max_stacked_effects=10

[PvP]
; Enable PvP-specific potion restrictions
enable_pvp_restrictions=false

; Restricted potion types in PvP zones
pvp_restricted_potions=

[Logging]
; Enable detailed potion usage logging
log_potion_usage=false

; Log blocked potion attempts
log_blocked_attempts=true

[Description]
; PotionMgr module provides:
; - Prevention of potion effect stacking exploits
; - Enforcement of proper potion usage delays
; - Restriction of certain cash item potions
; - PvP-specific potion limitations
; - Comprehensive potion usage validation
; - Effect limitation checks to prevent abuse
