[General]
; Player Movement System Module
; This module provides movement validation and anti-cheat measures
enabled=true

[Validation]
; Enable movement validation
enable_move_validation=true

; Enable movement speed checking
enable_speed_check=true

; Enable position validity checking
enable_position_check=true

; Enable distance validation
enable_distance_validation=true

[Limits]
; Maximum movement distance per update (units)
max_move_distance=1000.0

; Maximum movement speed (units per second)
max_move_speed=100.0

; Maximum coordinate value (prevents extreme positions)
max_coordinate_value=100000.0

; Minimum time between moves (milliseconds)
min_move_interval=50

[AntiCheat]
; Enable teleport detection
enable_teleport_detection=true

; Enable speed hack detection
enable_speed_hack_detection=true

; Enable position hack detection
enable_position_hack_detection=true

; Teleport distance threshold
teleport_threshold=500.0

; Speed hack multiplier threshold
speed_hack_threshold=2.0

[Tracking]
; Track player movement statistics
enable_movement_tracking=true

; Maximum movement history per player
max_movement_history=100

; Movement data cleanup interval (milliseconds)
movement_cleanup_interval=300000

[Restrictions]
; Block movement during certain states
block_during_trade=false

; Block movement during combat
block_during_combat=false

; Block movement in safe zones
block_in_safe_zones=false

; Block underground movement
block_underground=true

[Performance]
; Enable movement data caching
enable_movement_caching=true

; Maximum cached movement data entries
max_cached_entries=1000

; Cache cleanup interval (milliseconds)
cache_cleanup_interval=600000

[Logging]
; Enable movement logging
log_movement=false

; Log blocked movements
log_blocked_movements=true

; Log speed violations
log_speed_violations=true

; Log position violations
log_position_violations=true

; Log teleport detections
log_teleport_detections=true

[Description]
; PlayerMoveSystem module provides:
; - Movement validation and anti-cheat protection
; - Speed hack detection and prevention
; - Teleport detection and blocking
; - Position validity checking
; - Movement distance and speed limits
; - Player movement tracking and statistics
; - Performance-optimized movement data caching
; - Comprehensive movement logging and monitoring
