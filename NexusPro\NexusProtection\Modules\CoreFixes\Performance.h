#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/Global__GlobalInfo.hpp"
#include <chrono>
#include <unordered_map>
#include <mutex>

namespace NexusPro
{
    namespace CoreFixes
    {
        struct PerformanceMetrics
        {
            uint64_t totalCalls{ 0 };
            uint64_t totalTime{ 0 }; // microseconds
            uint64_t minTime{ UINT64_MAX };
            uint64_t maxTime{ 0 };
            double avgTime{ 0.0 };
        };

        class CPerformance : public IModuleInterface, public SingletonHelper::CSingleton<CPerformance>
        {
        public:
            CPerformance() = default;
            virtual ~CPerformance() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "Performance"; }

            // Configuration
            void Configure(bool enableOptimizations, bool enableMetrics, bool enableSIMD,
                          bool logPerformance, uint32_t metricsInterval);

            // Performance monitoring
            void UpdateMetrics(const std::string& functionName, uint64_t executionTime);
            PerformanceMetrics GetMetrics(const std::string& functionName) const;
            void ResetMetrics();
            void LogPerformanceReport();

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnableOptimizations{ true };
            bool m_bEnableMetrics{ false };
            bool m_bEnableSIMD{ true };
            bool m_bLogPerformance{ false };
            uint32_t m_metricsInterval{ 60000 }; // 1 minute

            // Performance tracking
            std::unordered_map<std::string, PerformanceMetrics> m_performanceMetrics;
            mutable std::mutex m_metricsMutex;
            std::chrono::steady_clock::time_point m_lastReport;

        private:
            // Hook functions - Optimized math operations
            static float WINAPIV GetSqrt(
                float* fPos,
                float* fTar,
                ATF::Global::Info::GetSqrt667_ptr next);

            static float WINAPIV Get3DSqrt(
                float* fPos,
                float* fTar,
                ATF::Global::Info::Get3DSqrt435_ptr next);

            static float WINAPIV GetYAngle(
                float* fPos,
                float* fTar,
                ATF::Global::Info::GetYAngle700_ptr next);

            static float WINAPIV GetDist(
                const float* fPos,
                const float* fTar,
                ATF::Global::Info::GetDist498_ptr next);

        private:
            // Helper functions
            static constexpr double PiToAngle(float value)
            {
                return ((double)(value) * 65535.0) / (2.0 * 3.1415926535);
            }

            // Optimized math functions
            static float FastSqrt2D(float* fPos, float* fTar);
            static float FastSqrt3D(float* fPos, float* fTar);
            static float FastDistance(const float* fPos, const float* fTar);
            static float FastYAngle(float* fPos, float* fTar);

            // Performance measurement
            class PerformanceTimer
            {
            public:
                PerformanceTimer(const std::string& name);
                ~PerformanceTimer();

            private:
                std::string m_functionName;
                std::chrono::high_resolution_clock::time_point m_startTime;
            };

        private:
            static CPerformance* s_instance;
        };

        // Macro for performance measurement
        #define PERF_TIMER(name) CPerformance::PerformanceTimer timer(name)
    }
}
