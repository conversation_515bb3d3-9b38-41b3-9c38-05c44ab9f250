#include "pch.h"
#include "PostSystem.h"
#include "../../Common/Helpers/ModuleHook.h"
#include "../../Core/ConfigManager.h"
#include "../../Common/Helpers/Logger.h"

namespace NexusPro
{
    namespace CoreFixes
    {
        CPostSystem* CPostSystem::s_instance = nullptr;

        bool CPostSystem::Initialize()
        {
            if (m_bInitialized)
                return true;

            s_instance = this;

            // Load configuration
            auto& configManager = ConfigManager::GetInstance();
            m_bEnablePostFixes = configManager.GetModuleConfigBool("corefix.post_system", "Features", "enable_post_fixes", true);
            m_bEnableValidation = configManager.GetModuleConfigBool("corefix.post_system", "Validation", "enable_validation", true);
            m_bEnableMetrics = configManager.GetModuleConfigBool("corefix.post_system", "Monitoring", "enable_metrics", false);
            m_maxPostsPerPlayer = configManager.GetModuleConfigInt("corefix.post_system", "Limits", "max_posts_per_player", 100);
            m_maxGoldPerPost = configManager.GetModuleConfigInt("corefix.post_system", "Limits", "max_gold_per_post", 1000000);
            m_bLogPostActivity = configManager.GetModuleConfigBool("corefix.post_system", "Logging", "log_post_activity", false);

            // Hook post system functions
            if (m_bEnablePostFixes)
            {
                enable_hook(&ATF::CPostSystemManager::CheckRegister, &CPostSystem::CheckRegister);
                enable_hook(&ATF::CMainThread::Load_ReturnPost_Complete, &CPostSystem::CMainThread_Load_ReturnPost_Complete);
                enable_hook(&ATF::CMainThread::Load_PostStorage_Complete, &CPostSystem::CMainThread_Load_PostStorage_Complete);
            }

            m_bInitialized = true;

            LOG_INFO("PostSystem", "Post system fixes initialized - Post Fixes: " + std::to_string(m_bEnablePostFixes) +
                     ", Validation: " + std::to_string(m_bEnableValidation) +
                     ", Metrics: " + std::to_string(m_bEnableMetrics) +
                     ", Max Posts: " + std::to_string(m_maxPostsPerPlayer) +
                     ", Max Gold: " + std::to_string(m_maxGoldPerPost));

            return true;
        }

        void CPostSystem::Shutdown()
        {
            if (!m_bInitialized)
                return;

            cleanup_all_hook();

            std::lock_guard<std::mutex> lock(m_postDataMutex);
            m_playerPostCounts.clear();

            s_instance = nullptr;
            m_bInitialized = false;

            LOG_INFO("PostSystem", "Post system fixes shutdown");
        }

        void CPostSystem::Configure(bool enablePostFixes, bool enableValidation, bool enableMetrics,
                                   uint32_t maxPostsPerPlayer, uint64_t maxGoldPerPost, bool logPostActivity)
        {
            m_bEnablePostFixes = enablePostFixes;
            m_bEnableValidation = enableValidation;
            m_bEnableMetrics = enableMetrics;
            m_maxPostsPerPlayer = maxPostsPerPlayer;
            m_maxGoldPerPost = maxGoldPerPost;
            m_bLogPostActivity = logPostActivity;
        }

        bool CPostSystem::ValidatePostRegistration(ATF::CPlayer* pPlayer, ATF::_STORAGE_POS_INDIV* pItemInfo,
                                                  uint32_t dwGold, ATF::_STORAGE_LIST::_db_con** pItem)
        {
            if (!pPlayer || !m_bEnableValidation)
                return true;

            // Validate post data
            if (!ValidatePostData(pPlayer, dwGold))
                return false;

            // Validate item data if present
            if (pItemInfo && pItem && !ValidateItemData(pItemInfo, pItem))
                return false;

            return true;
        }

        void CPostSystem::UpdatePostMetrics(const std::string& operation, uint32_t count, uint64_t gold)
        {
            if (!m_bEnableMetrics)
                return;

            std::lock_guard<std::mutex> lock(m_postDataMutex);

            if (operation == "sent")
            {
                m_metrics.totalPostsSent += count;
                m_metrics.totalGoldTransferred += gold;
            }
            else if (operation == "received")
            {
                m_metrics.totalPostsReceived += count;
            }
            else if (operation == "returned")
            {
                m_metrics.totalPostsReturned += count;
            }
            else if (operation == "failed")
            {
                m_metrics.failedPosts += count;
            }
        }

        bool CPostSystem::ValidatePostData(ATF::CPlayer* pPlayer, uint32_t dwGold)
        {
            if (!pPlayer)
                return false;

            // Check gold limit
            if (dwGold > m_maxGoldPerPost)
            {
                if (m_bLogPostActivity)
                {
                    LOG_WARNING("PostSystem", "Post gold limit exceeded - Player: " + std::string(pPlayer->m_wszPlayerName) +
                               ", Gold: " + std::to_string(dwGold) + ", Limit: " + std::to_string(m_maxGoldPerPost));
                }
                return false;
            }

            // Check player post count limit
            std::lock_guard<std::mutex> lock(m_postDataMutex);
            uint32_t playerSerial = pPlayer->m_dwSerial;
            uint32_t currentPosts = m_playerPostCounts[playerSerial];

            if (currentPosts >= m_maxPostsPerPlayer)
            {
                if (m_bLogPostActivity)
                {
                    LOG_WARNING("PostSystem", "Player post limit exceeded - Player: " + std::string(pPlayer->m_wszPlayerName) +
                               ", Current: " + std::to_string(currentPosts) + ", Limit: " + std::to_string(m_maxPostsPerPlayer));
                }
                return false;
            }

            return true;
        }

        bool CPostSystem::ValidateItemData(ATF::_STORAGE_POS_INDIV* pItemInfo, ATF::_STORAGE_LIST::_db_con** pItem)
        {
            if (!pItemInfo || !pItem)
                return true; // No item to validate

            // Basic item validation
            // Additional validation logic would be implemented here
            return true;
        }

        void CPostSystem::ProcessReturnPostList(ATF::CPlayer* pPlayer, ATF::_qry_case_post_return_list_get* pInfo)
        {
            if (!pPlayer || !pInfo)
                return;

            if (pInfo->byProcRet == 1)
                return;

            if (!pPlayer->m_bLoad || pPlayer->m_bPostLoad)
                return;

            pPlayer->m_bPostLoad = true;

            auto pReturnPostStorage = &pPlayer->m_Param.m_ReturnPostStorage;
            uint32_t processedPosts = 0;

            for (unsigned int j = 0; j < pInfo->dwCount; ++j)
            {
                auto pPost = pReturnPostStorage->AddReturnPost(
                    pInfo->List[j].byErr,
                    pInfo->List[j].dwSerial,
                    pInfo->List[j].byState,
                    pInfo->List[j].wszRecvName,
                    pInfo->List[j].wszTitle,
                    pInfo->List[j].wszContent,
                    pInfo->List[j].key,
                    pInfo->List[j].dwDur,
                    pInfo->List[j].dwUpt,
                    pInfo->List[j].dwGold,
                    pInfo->List[j].lnUID);

                if (!pPost)
                    continue;

                processedPosts++;

                if (pPost->m_Key.IsFilled() || pPost->m_dwGold)
                {
                    ATF::Global::s_MgrItemHistory->post_returnreceive(
                        pPost,
                        pPlayer->m_szItemHistoryFileName);
                }
            }

            UpdatePostMetrics("returned", processedPosts);

            if (m_bLogPostActivity)
            {
                LOG_INFO("PostSystem", "Return posts loaded - Player: " + std::string(pPlayer->m_wszPlayerName) +
                        ", Count: " + std::to_string(processedPosts));
            }
        }

        void CPostSystem::ProcessPostStorageList(ATF::CPlayer* pPlayer, ATF::_qry_case_post_storage_list_get* pInfo)
        {
            if (!pPlayer || !pInfo)
                return;

            if (pInfo->byProcRet == 1)
                return;

            if (!pPlayer->m_bLoad || pPlayer->m_bPostLoad)
                return;

            pPlayer->m_bPostLoad = true;

            auto pPostStorage = &pPlayer->m_Param.m_PostStorage;
            uint32_t processedPosts = 0;

            for (unsigned int j = 0; j < pInfo->dwCount; ++j)
            {
                auto pPost = pPostStorage->AddPost(
                    pInfo->List[j].byErr,
                    pInfo->List[j].dwSerial,
                    pInfo->List[j].byState,
                    pInfo->List[j].wszSendName,
                    pInfo->List[j].wszTitle,
                    pInfo->List[j].wszContent,
                    pInfo->List[j].key,
                    pInfo->List[j].dwDur,
                    pInfo->List[j].dwUpt,
                    pInfo->List[j].dwGold,
                    pInfo->List[j].lnUID);

                if (pPost)
                    processedPosts++;
            }

            UpdatePostMetrics("received", processedPosts);

            if (m_bLogPostActivity)
            {
                LOG_INFO("PostSystem", "Posts loaded - Player: " + std::string(pPlayer->m_wszPlayerName) +
                        ", Count: " + std::to_string(processedPosts));
            }
        }

        void WINAPIV CPostSystem::CMainThread_Load_ReturnPost_Complete(
            ATF::CMainThread* pObj,
            char* pData,
            ATF::Info::CMainThreadLoad_ReturnPost_Complete114_ptr next)
        {
            if (!pObj || !pData || !s_instance)
            {
                next(pObj, pData);
                return;
            }

            ATF::_qry_case_post_return_list_get* pInfo = (ATF::_qry_case_post_return_list_get*)pData;
            ATF::CPlayer* pPlayer = ATF::Global::GetPtrPlayerFromSerial(ATF::Global::g_Player, ATF::Global::max_player, pInfo->dwMasterSerial);

            if (pPlayer)
            {
                s_instance->ProcessReturnPostList(pPlayer, pInfo);
            }
            else
            {
                // Call original function if player not found
                next(pObj, pData);
            }
        }

        void WINAPIV CPostSystem::CMainThread_Load_PostStorage_Complete(
            ATF::CMainThread* pObj,
            char* pData,
            ATF::Info::CMainThreadLoad_PostStorage_Complete112_ptr next)
        {
            if (!pObj || !pData || !s_instance)
            {
                next(pObj, pData);
                return;
            }

            ATF::_qry_case_post_storage_list_get* pInfo = (ATF::_qry_case_post_storage_list_get*)pData;
            ATF::CPlayer* pPlayer = ATF::Global::GetPtrPlayerFromSerial(ATF::Global::g_Player, ATF::Global::max_player, pInfo->dwMasterSerial);

            if (pPlayer)
            {
                s_instance->ProcessPostStorageList(pPlayer, pInfo);
            }
            else
            {
                // Call original function if player not found
                next(pObj, pData);
            }
        }

        char WINAPIV CPostSystem::CheckRegister(
            ATF::CPostSystemManager* pObj,
            ATF::CPlayer* pOne,
            ATF::_STORAGE_POS_INDIV* pItemInfo,
            unsigned int dwGold,
            ATF::_STORAGE_LIST::_db_con** pItem,
            ATF::Info::CPostSystemManagerCheckRegister4_ptr next)
        {
            if (!pObj || !pOne || !s_instance)
                return next(pObj, pOne, pItemInfo, dwGold, pItem);

            // Validate post registration
            if (!s_instance->ValidatePostRegistration(pOne, pItemInfo, dwGold, pItem))
            {
                if (s_instance->m_bLogPostActivity)
                {
                    LOG_WARNING("PostSystem", "Post registration blocked - Player: " + std::string(pOne->m_wszPlayerName) +
                               ", Gold: " + std::to_string(dwGold));
                }
                return 1; // Block registration
            }

            // Update player post count
            {
                std::lock_guard<std::mutex> lock(s_instance->m_postDataMutex);
                s_instance->m_playerPostCounts[pOne->m_dwSerial]++;
            }

            s_instance->UpdatePostMetrics("sent", 1, dwGold);

            if (s_instance->m_bLogPostActivity)
            {
                LOG_INFO("PostSystem", "Post registered - Player: " + std::string(pOne->m_wszPlayerName) +
                        ", Gold: " + std::to_string(dwGold));
            }

            return next(pObj, pOne, pItemInfo, dwGold, pItem);
        }
    }
}
