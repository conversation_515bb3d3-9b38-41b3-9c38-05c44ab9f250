#pragma once
#include "pch.h"

namespace NexusProtection
{
    namespace Core
    {
        class CModuleManager
        {
        public:
            CModuleManager();
            ~CModuleManager();

            // Lifecycle methods
            void Initialize();
            void LoadAllModules();
            void UnloadAllModules();
            void OnZoneStart();
            void ExecuteLoop();

            // Configuration
            void ConfigureModules(const rapidjson::Value& config);

            // Module registration (for embedded modules)
            void RegisterModule(std::shared_ptr<NexusProtection::Module::IModule> module);

        private:
            // Module storage
            using ModuleMap = std::unordered_map<std::string, std::shared_ptr<NexusProtection::Module::IModule>>;
            ModuleMap m_modules;
            std::mutex m_modulesMutex;

            // Register all embedded modules
            void RegisterEmbeddedModules();

            // Core fixes
            void RegisterCoreFixes();

            // Player extensions
            void RegisterPlayerExtensions();

            // Addon modules
            void RegisterAddonModules();
        };
    }
}
