[General]
; Player Extensions Module
; This module provides extended player functionality including set items, attack delays, and PvP tracking
enabled=true

[Features]
; Enable set item detection and management fixes
enable_set_item_fix=true

; Enable attack delay validation and management
enable_attack_delay_fix=true

; Enable PvP killer tracking system
enable_pvp_tracking=true

; Enable movement validation
enable_movement_validation=true

[Timing]
; Set item update interval in milliseconds
set_item_update_interval=10000

; PvP killer list cleanup interval in milliseconds
pvp_cleanup_interval=300000

; Attack delay collision adjustment in milliseconds
attack_delay_collision=50

[AttackDelays]
; Enable unit attack delay validation
enable_unit_delay=true

; Enable siege attack delay validation
enable_siege_delay=true

; Enable normal attack delay validation
enable_normal_delay=true

; Enable force attack delay validation
enable_force_delay=true

; Enable skill attack delay validation
enable_skill_delay=true

[SetItems]
; Maximum set items to track per player
max_set_items_per_player=20

; Enable set item effect validation
enable_set_effect_validation=true

; Send set item info during siege mode
send_during_siege=false

; Send set item info while riding units
send_while_riding=false

[PvP]
; Maximum PvP killers to track per player
max_pvp_killers_per_player=100

; Enable automatic PvP killer list cleanup
enable_auto_cleanup=true

; PvP tracking timeout in milliseconds
pvp_tracking_timeout=1800000

[Movement]
; Enable movement speed validation
enable_speed_validation=true

; Enable position validation
enable_position_validation=true

; Maximum movement distance per update
max_movement_distance=1000.0

[Performance]
; Enable player data caching
enable_data_caching=true

; Maximum cached players
max_cached_players=1000

; Cache cleanup interval in milliseconds
cache_cleanup_interval=600000

[Logging]
; Enable detailed player action logging
log_player_actions=false

; Log set item updates
log_set_item_updates=false

; Log attack delay events
log_attack_delays=false

; Log PvP tracking events
log_pvp_tracking=false

[Description]
; PlayerEx module provides:
; - Extended player functionality and data management
; - Set item detection, validation, and effect management
; - Attack delay validation for all attack types (unit, siege, normal, force, skill)
; - PvP killer tracking and automatic cleanup
; - Movement validation and anti-cheat measures
; - Performance optimizations for player data handling
; - Thread-safe player data management
; - Comprehensive player action logging
