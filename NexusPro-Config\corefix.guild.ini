[General]
; Guild System Fix Module
; This module fixes various guild-related issues and exploits
enabled=true

[Battle]
; Guild battle cost in gold
cost_gold=5000

; Minimum guild level required for battles
min_level_for_battle=10

; Maximum concurrent guild battles
max_concurrent_battles=10

[Fixes]
; Enable guild battle management fixes
enable_battle_fix=true

; Enable member expulsion fixes
enable_expulse_fix=true

; Enable honor guild money management fixes
enable_honor_guild_fix=true

; Enable guild battle cost validation
enable_cost_validation=true

[Restrictions]
; Prevent member expulsion during guild battles
block_expulsion_during_battle=true

; Minimum time between guild battles (hours)
min_battle_interval=24

; Maximum guild members
max_guild_members=100

[Permissions]
; Require specific guild rank for battle management
require_rank_for_battle=true

; Minimum rank level for battle management (0=leader, 1=officer, etc.)
min_rank_for_battle=1

; Require specific guild rank for member expulsion
require_rank_for_expulsion=true

; Minimum rank level for member expulsion
min_rank_for_expulsion=1

[Logging]
; Enable detailed guild action logging
log_guild_actions=false

; Log guild battle events
log_battle_events=true

; Log member management events
log_member_events=true

; Log guild financial transactions
log_financial_events=false

[Description]
; Guild module provides:
; - Fixed guild battle acceptance/refusal system
; - Proper guild battle cost validation and deduction
; - Member expulsion fixes and restrictions
; - Honor guild money management fixes
; - Guild battle state validation
; - Prevention of exploits during guild battles
; - Comprehensive guild action logging
