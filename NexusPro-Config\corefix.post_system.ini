[General]
; Post System Fix Module
; This module fixes and enhances the mail/post system
enabled=true

[Features]
; Enable post system fixes
enable_post_fixes=true

; Enable post system validation
enable_validation=true

; Enable post system monitoring
enable_monitoring=false

[Validation]
; Enable post data validation
enable_post_validation=true

; Enable item validation in posts
enable_item_validation=true

; Enable gold validation in posts
enable_gold_validation=true

; Enable recipient validation
enable_recipient_validation=true

[Limits]
; Maximum posts per player
max_posts_per_player=100

; Maximum gold per post
max_gold_per_post=1000000

; Maximum post title length
max_title_length=50

; Maximum post content length
max_content_length=500

; Maximum attachment items per post
max_items_per_post=5

[Security]
; Enable anti-spam measures
enable_anti_spam=true

; Minimum time between posts (seconds)
min_post_interval=30

; Maximum posts per hour
max_posts_per_hour=20

; Enable post content filtering
enable_content_filtering=false

[Storage]
; Maximum return posts per player
max_return_posts=50

; Post expiration time (days)
post_expiration_days=30

; Auto-delete expired posts
auto_delete_expired=true

; Enable post compression
enable_post_compression=false

[Monitoring]
; Enable post metrics collection
enable_metrics=false

; Enable post activity logging
log_post_activity=false

; Enable post statistics
enable_statistics=false

; Metrics reporting interval (minutes)
metrics_interval=60

[Performance]
; Enable post caching
enable_post_caching=true

; Maximum cached posts
max_cached_posts=1000

; Cache cleanup interval (minutes)
cache_cleanup_interval=30

[Logging]
; Log post registrations
log_registrations=false

; Log post retrievals
log_retrievals=false

; Log post returns
log_returns=false

; Log blocked posts
log_blocked_posts=true

; Log post errors
log_errors=true

[Description]
; PostSystem module provides:
; - Post system fixes and stability improvements
; - Post validation and security measures
; - Anti-spam and abuse prevention
; - Post limits and restrictions
; - Post storage and expiration management
; - Performance optimizations for post handling
; - Comprehensive post activity monitoring
; - Post metrics and statistics collection
