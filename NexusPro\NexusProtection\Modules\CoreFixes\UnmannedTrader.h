#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CUnmannedTraderControllerInfo.hpp"
#include <unordered_map>
#include <mutex>

namespace NexusPro
{
    namespace CoreFixes
    {
        struct UnmannedTraderMetrics
        {
            uint32_t totalTransactions{ 0 };
            uint32_t successfulBuys{ 0 };
            uint32_t failedBuys{ 0 };
            uint32_t totalRegistrations{ 0 };
            uint32_t totalReRegistrations{ 0 };
            uint64_t totalGoldTransferred{ 0 };
        };

        struct TraderValidationData
        {
            uint32_t playerSerial{ 0 };
            uint32_t transactionCount{ 0 };
            std::chrono::steady_clock::time_point lastTransaction;
            bool isValidating{ true };
        };

        class CUnmannedTrader : public IModuleInterface, public SingletonHelper::CSingleton<CUnmannedTrader>
        {
        public:
            CUnmannedTrader() = default;
            virtual ~CUnmannedTrader() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "UnmannedTrader"; }

            // Configuration
            void Configure(bool enableTraderFixes, bool enableValidation, bool enableMetrics,
                          uint32_t maxTransactionsPerHour, uint64_t maxGoldPerTransaction, bool logTraderActivity);

            // Trader validation
            bool ValidateTraderTransaction(ATF::CPlayer* pPlayer, uint32_t dwGold);
            bool ValidateTraderRegistration(ATF::CPlayer* pPlayer);
            void UpdateTraderMetrics(const std::string& operation, uint32_t count = 1, uint64_t gold = 0);

        private:
            bool m_bInitialized{ false };

            // Configuration
            bool m_bEnableTraderFixes{ true };
            bool m_bEnableValidation{ true };
            bool m_bEnableMetrics{ false };
            uint32_t m_maxTransactionsPerHour{ 100 };
            uint64_t m_maxGoldPerTransaction{ 10000000 };
            bool m_bLogTraderActivity{ false };

            // Trader tracking
            UnmannedTraderMetrics m_metrics;
            std::unordered_map<uint32_t, TraderValidationData> m_traderData;
            mutable std::mutex m_traderDataMutex;

        private:
            // Hook functions
            static void WINAPIV CompleteBuy(
                ATF::CUnmannedTraderUserInfoTable* pObj,
                char byRet,
                char* pLoadData,
                ATF::CUnmannedTraderTradeInfo* pkTradeInfo,
                ATF::Info::CUnmannedTraderUserInfoTableCompleteBuy16_ptr next);

            static void WINAPIV ReRegist(
                ATF::CUnmannedTraderUserInfo* pObj,
                char byType,
                ATF::_unmannedtrader_re_regist_request_clzo* pRequest,
                ATF::CLogFile* pkLogger,
                ATF::Info::CUnmannedTraderUserInfoReRegist110_ptr next);

            static bool WINAPIV UpdateReRegist(
                ATF::CUnmannedTraderController* pObj,
                char* pData,
                ATF::Info::CUnmannedTraderControllerUpdateReRegist108_ptr next);

            static void WINAPIV CompleteReRegist(
                ATF::CUnmannedTraderUserInfoTable* pObj,
                char* pData,
                ATF::Info::CUnmannedTraderUserInfoTableCompleteReRegist22_ptr next);

            static void WINAPIV NotifyRegistItem(
                ATF::CUnmannedTraderUserInfo* pObj,
                ATF::CPlayer* pPlayer,
                ATF::_unmannedtrader_regist_item_result_zocl* pResult,
                ATF::Info::CUnmannedTraderUserInfoNotifyRegistItem112_ptr next);

        private:
            // Helper functions
            bool ValidateTransactionLimits(ATF::CPlayer* pPlayer);
            bool ValidateGoldAmount(uint64_t dwGold);
            void ProcessBuyTransaction(ATF::CUnmannedTraderUserInfoTable* pObj, char* pLoadData, 
                                     ATF::CUnmannedTraderTradeInfo* pkTradeInfo);
            void ProcessReRegistration(ATF::CUnmannedTraderUserInfo* pObj, char byType,
                                     ATF::_unmannedtrader_re_regist_request_clzo* pRequest);

        private:
            static CUnmannedTrader* s_instance;
        };
    }
}
