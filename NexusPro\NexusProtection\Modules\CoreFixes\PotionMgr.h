#pragma once

#include "../../Common/Interfaces/ModuleInterface.h"
#include "../../Common/Helpers/SingletonHelper.h"
#include "../../Common/ATF/CPotionMgrInfo.hpp"

namespace NexusPro
{
    namespace CoreFixes
    {
        class CPotionMgr : public IModuleInterface, public SingletonHelper::CSingleton<CPotionMgr>
        {
        public:
            CPotionMgr() = default;
            virtual ~CPotionMgr() = default;

            // IModuleInterface implementation
            virtual bool Initialize() override;
            virtual void Shutdown() override;
            virtual const char* GetModuleName() const override { return "PotionMgr"; }

            // Configuration
            void Configure(bool enableStackingFix, bool enableDelayFix, bool enableEffectLimitFix,
                          bool enableCashItemFix, bool enablePvpPotionFix);

        private:
            bool m_bInitialized{ false };

            // Configuration flags
            bool m_bEnableStackingFix{ true };
            bool m_bEnableDelayFix{ true };
            bool m_bEnableEffectLimitFix{ true };
            bool m_bEnableCashItemFix{ true };
            bool m_bEnablePvpPotionFix{ true };

            // Logging
            bool m_bLogPotionUsage{ false };

        private:
            // Hook functions
            static int WINAPIV PreCheckPotion(
                ATF::CPotionMgr* pObj,
                ATF::CPlayer* pUsePlayer,
                ATF::CCharacter** pTargetCharacter,
                ATF::_PotionItem_fld* pfB,
                unsigned int nCurTime,
                ATF::_skill_fld* pFld,
                bool bCheckDist,
                ATF::Info::CPotionMgrPreCheckPotion22_ptr next);

            // Helper functions
            static bool CheckEffectLimitation(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld);
            static bool CheckPotionStacking(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld);
            static bool CheckPotionDelay(ATF::CPlayer* pPlayer, unsigned int nCurTime);
            static bool CheckCashItemRestriction(ATF::_skill_fld* pFld);
            static bool CheckPvpPotionRestriction(ATF::CPlayer* pPlayer, ATF::_skill_fld* pFld);

        private:
            static CPotionMgr* s_instance;
        };
    }
}
